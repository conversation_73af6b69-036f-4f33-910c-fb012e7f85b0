# XANO Backend Integration Guide

This document outlines the complete integration of the Albatross CRM with XANO backend services, replacing mock data with real database operations.

## Overview

The Albatross CRM has been successfully integrated with XANO backend services across all major modules:

- ✅ **Dashboard Module** - Metrics and overview data
- ✅ **Roster/Golfer Management** - CRUD operations for golfer data
- ✅ **Tee Time Management** - Calendar and booking operations
- ✅ **Pro Shop Module** - Inventory and product management
- ✅ **19th Hole Module** - Food & beverage management
- ✅ **Analytics Module** - Reporting and analytics data
- ✅ **Back Office Module** - Staff management and operations

## Configuration

### Environment Variables

The application uses environment variables to control backend integration:

```bash
# Course Configuration
REACT_APP_COURSE_ID=6793f989213768ac24c381e4

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:3000
REACT_APP_XANO_BASE_URL=https://x8ki-letl-twmt.n7.xano.io/api:uP98JIiJ

# Development Settings - Set to false to use XANO backend
REACT_APP_USE_MOCK_DATA=false
```

### Switching Between Mock and Real Data

To switch between mock data (for development) and XANO backend:

1. **Use Mock Data**: Set `REACT_APP_USE_MOCK_DATA=true`
2. **Use XANO Backend**: Set `REACT_APP_USE_MOCK_DATA=false`

## API Client Architecture

### XanoAPIClient

The main API client (`src/api/XanoAPIClient.ts`) provides:

- **Authentication**: JWT token management with automatic refresh
- **Error Handling**: Comprehensive error handling with fallbacks
- **Type Safety**: TypeScript interfaces for all API responses
- **Caching**: Built-in request/response caching where appropriate

### Service Layer

Each module has its own service layer (`src/services/api.ts`) that:

- Handles the switch between mock and real data
- Transforms XANO responses to match frontend interfaces
- Provides consistent error handling across all modules
- Maintains backward compatibility with existing components

## Module Integration Details

### Dashboard Module

**Endpoints Used:**
- `GET /dashboard/metrics` - Overview metrics and KPIs

**Data Transformation:**
```typescript
// XANO Response → Frontend Interface
{
  total_revenue: number,
  total_bookings: number,
  total_golfers: number,
  revenue_change: number,
  bookings_change: number,
  golfers_change: number
}
```

### Roster/Golfer Management

**Endpoints Used:**
- `GET /golfers` - List golfers with filtering
- `POST /golfers` - Create new golfer
- `PUT /golfers/:id` - Update golfer
- `DELETE /golfers/:id` - Delete golfer

**Key Features:**
- Real-time search and filtering
- Bulk operations support
- Automatic data transformation between XANO and frontend formats

### Tee Time Management

**Endpoints Used:**
- `GET /tee-times` - List tee times by date/view
- `POST /tee-times` - Create new tee time
- `PUT /tee-times/:id` - Update tee time
- `DELETE /tee-times/:id` - Cancel tee time

**Features:**
- Daily, weekly, monthly views
- Real-time availability updates
- Booking conflict resolution

### Pro Shop Module

**Endpoints Used:**
- `GET /products` - List inventory items
- `POST /products` - Add new product
- `PUT /products/:id` - Update product
- `DELETE /products/:id` - Remove product

**Features:**
- Category-based filtering
- Inventory tracking
- Price management

### 19th Hole Module

**Endpoints Used:**
- `GET /menu-items` - List menu items
- `POST /menu-items` - Add menu item
- `PUT /menu-items/:id` - Update menu item
- `DELETE /menu-items/:id` - Remove menu item
- `GET /orders` - List orders
- `POST /orders` - Create order

**Features:**
- Menu management
- Order tracking
- Category organization

### Analytics Module

**Endpoints Used:**
- `GET /analytics` - Analytics data with various parameters

**Features:**
- Revenue reporting
- Booking trends
- Performance metrics
- Custom date ranges

### Back Office Module

**Endpoints Used:**
- `GET /staff` - List staff members
- `POST /staff` - Add staff member
- `PUT /staff/:id` - Update staff member

**Features:**
- Staff management
- Shift tracking
- Payroll integration

## Error Handling

The integration includes comprehensive error handling:

### Network Errors
- Automatic retry logic for transient failures
- Graceful degradation to cached data when available
- User-friendly error messages

### Authentication Errors
- Automatic token refresh
- Redirect to login when authentication fails
- Secure token storage

### Data Validation Errors
- Client-side validation before API calls
- Server-side error message display
- Form field highlighting for validation errors

## Testing

### Integration Tests

Run the integration test suite:

```bash
npm test -- --testPathPattern=integration.test.ts
```

The test suite validates:
- All API endpoints are accessible
- Data transformation works correctly
- Error handling functions properly
- Configuration switches work as expected

### Manual Testing Checklist

1. **Dashboard**
   - [ ] Metrics load correctly
   - [ ] Time range switching works
   - [ ] Error states display properly

2. **Roster Management**
   - [ ] Golfer list loads and displays
   - [ ] Search and filtering work
   - [ ] Create/edit/delete operations function
   - [ ] Bulk operations work correctly

3. **Tee Time Management**
   - [ ] Calendar views load correctly
   - [ ] Booking creation works
   - [ ] Time slot availability updates
   - [ ] Booking modifications function

4. **Pro Shop**
   - [ ] Inventory list loads
   - [ ] Product creation/editing works
   - [ ] Category filtering functions
   - [ ] Stock management updates

5. **19th Hole**
   - [ ] Menu items display correctly
   - [ ] Order management works
   - [ ] Category organization functions

6. **Analytics**
   - [ ] Reports generate correctly
   - [ ] Charts display data properly
   - [ ] Date range filtering works

7. **Back Office**
   - [ ] Staff list loads
   - [ ] Shift management works
   - [ ] Employee operations function

## Deployment Considerations

### Production Setup

1. **Environment Variables**: Ensure all production environment variables are set correctly
2. **XANO Configuration**: Verify XANO workspace and API endpoints are configured for production
3. **Authentication**: Set up proper authentication tokens and refresh mechanisms
4. **Error Monitoring**: Implement error tracking for production issues

### Performance Optimization

- **Caching**: Implement appropriate caching strategies for frequently accessed data
- **Pagination**: Use pagination for large data sets
- **Lazy Loading**: Implement lazy loading for non-critical data
- **Debouncing**: Use debounced search to reduce API calls

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure XANO workspace allows requests from your domain
2. **Authentication Failures**: Check token validity and refresh logic
3. **Data Format Mismatches**: Verify data transformation functions
4. **Network Timeouts**: Implement appropriate timeout handling

### Debug Mode

Enable debug logging by setting:
```bash
REACT_APP_DEBUG_API=true
```

This will log all API requests and responses to the browser console.

## Future Enhancements

- **Real-time Updates**: Implement WebSocket connections for real-time data updates
- **Offline Support**: Add offline capability with local data synchronization
- **Advanced Caching**: Implement more sophisticated caching strategies
- **Performance Monitoring**: Add performance tracking for API calls
- **Data Validation**: Enhanced client-side validation with server-side schema validation

## Support

For issues related to XANO integration:

1. Check the integration test results
2. Review error logs in browser console
3. Verify XANO workspace configuration
4. Test API endpoints directly using the XANO interface
5. Check network connectivity and CORS settings
