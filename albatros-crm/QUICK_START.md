# Quick Start Guide - XANO Integration

## Current Status ✅

The Albatross CRM has been successfully integrated with XANO backend infrastructure. Currently, the application is configured to use **mock data** to ensure stability while XANO endpoints are being finalized.

## How to Use

### 1. Running the Application

```bash
cd albatros-crm
npm start
```

The application will start on `http://localhost:3000` and use mock data by default.

### 2. Data Source Toggle (Development)

In development mode, you'll see a **settings icon** in the top-right corner that allows you to:

- ✅ **Mock Data** (Default) - Stable, works offline, good for development
- ⚠️ **XANO Backend** - Real database, requires proper endpoint configuration

### 3. Switching to XANO Backend

When your XANO endpoints are ready:

1. Click the settings icon in the top-right corner
2. Toggle the switch to "XANO Backend"
3. Check browser console for any API errors
4. If errors occur, switch back to "Mock Data"

## Current Integration Status

### ✅ Fully Integrated Modules

All modules have been prepared for XANO integration:

- **Dashboard** - Metrics and KPIs
- **Roster Management** - Golfer CRUD operations
- **Tee Time Management** - Booking and scheduling
- **Pro Shop** - Inventory management
- **19th Hole** - Food & beverage management
- **Analytics** - Reporting and charts
- **Back Office** - Staff management

### 🔧 XANO Endpoints Expected

The integration expects these XANO endpoints:

```
GET /golfers              - List golfers
POST /golfers             - Create golfer
PUT /golfers/:id          - Update golfer
DELETE /golfers/:id       - Delete golfer

GET /tee-times            - List tee times
POST /tee-times           - Create booking
PUT /tee-times/:id        - Update booking
DELETE /tee-times/:id     - Cancel booking

GET /products             - List inventory
POST /products            - Add product
PUT /products/:id         - Update product
DELETE /products/:id      - Remove product

GET /menu-items           - List menu items
POST /menu-items          - Add menu item
PUT /menu-items/:id       - Update menu item
DELETE /menu-items/:id    - Remove menu item

GET /staff                - List staff
POST /staff               - Add staff member
PUT /staff/:id            - Update staff
DELETE /staff/:id         - Remove staff

GET /dashboard/metrics    - Dashboard overview
GET /analytics            - Analytics data
```

## Configuration

### Environment Variables

The application uses these environment variables (in `.env`):

```bash
# Course Configuration
REACT_APP_COURSE_ID=6793f989213768ac24c381e4

# XANO Configuration
REACT_APP_XANO_BASE_URL=https://x8ki-letl-twmt.n7.xano.io/api:uP98JIiJ

# Data Source Control
REACT_APP_USE_MOCK_DATA=true  # Set to false when XANO is ready
```

### Switching Data Sources

**Option 1: Environment Variable**
```bash
# In .env file
REACT_APP_USE_MOCK_DATA=false  # Use XANO
REACT_APP_USE_MOCK_DATA=true   # Use Mock Data
```

**Option 2: Development Toggle**
- Use the settings icon in the top-right corner (development only)

## Troubleshooting

### Common Issues

1. **"Failed to load" errors on every page**
   - Switch to Mock Data mode
   - Check XANO endpoint configuration
   - Verify CORS settings in XANO

2. **XANO endpoints not found (404)**
   - Verify endpoint URLs in XANO workspace
   - Check API path configuration
   - Ensure endpoints are published

3. **Authentication errors (401)**
   - Check if endpoints require authentication
   - Verify API tokens if needed

### Debug Mode

Enable detailed logging:

```bash
# In browser console
localStorage.setItem('debug', 'true')
```

This will log all API requests and responses.

## Testing

### Validate Integration

```bash
npm run validate-integration
```

### Run Integration Tests

```bash
npm run test:integration
```

## Next Steps

1. **Configure XANO Endpoints**: Set up the required endpoints in your XANO workspace
2. **Test Endpoints**: Use the validation script to test connectivity
3. **Switch to XANO**: Change `REACT_APP_USE_MOCK_DATA=false` when ready
4. **Monitor Errors**: Check browser console for any API issues

## Support

- Check `XANO_INTEGRATION.md` for detailed technical documentation
- Use the development toggle to switch between data sources
- Monitor browser console for API errors
- Test individual endpoints using the validation script

## File Structure

```
albatros-crm/
├── src/
│   ├── api/
│   │   └── XanoAPIClient.ts     # XANO API client
│   ├── services/
│   │   └── api.ts               # Service layer with fallbacks
│   ├── components/
│   │   └── shared/
│   │       └── DataSourceToggle.tsx  # Development toggle
│   └── config.ts                # Configuration
├── .env                         # Environment variables
├── XANO_INTEGRATION.md         # Detailed documentation
└── scripts/
    └── validate-integration.js  # Validation script
```

The application is ready to use with mock data and can be easily switched to XANO when your endpoints are configured! 🚀
