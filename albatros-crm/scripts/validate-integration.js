#!/usr/bin/env node

/**
 * XANO Integration Validation Script
 * 
 * This script validates that the XANO backend integration is working correctly
 * by testing key API endpoints and verifying responses.
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  XANO_BASE_URL: process.env.REACT_APP_XANO_BASE_URL || 'https://x8ki-letl-twmt.n7.xano.io/api:uP98JIiJ',
  COURSE_ID: process.env.REACT_APP_COURSE_ID || '6793f989213768ac24c381e4',
  USE_MOCK_DATA: process.env.REACT_APP_USE_MOCK_DATA === 'true',
};

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Create axios instance for XANO API
const xanoAPI = axios.create({
  baseURL: CONFIG.XANO_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

function addTestResult(name, passed, message, warning = false) {
  testResults.tests.push({ name, passed, message, warning });
  if (warning) {
    testResults.warnings++;
  } else if (passed) {
    testResults.passed++;
  } else {
    testResults.failed++;
  }
}

async function validateConfiguration() {
  log('\n📋 Validating Configuration...', 'bold');
  
  // Check environment variables
  if (!CONFIG.XANO_BASE_URL) {
    logError('XANO_BASE_URL not configured');
    addTestResult('XANO_BASE_URL', false, 'Environment variable not set');
    return false;
  }
  
  if (!CONFIG.COURSE_ID) {
    logError('COURSE_ID not configured');
    addTestResult('COURSE_ID', false, 'Environment variable not set');
    return false;
  }
  
  logSuccess(`XANO Base URL: ${CONFIG.XANO_BASE_URL}`);
  logSuccess(`Course ID: ${CONFIG.COURSE_ID}`);
  logInfo(`Using Mock Data: ${CONFIG.USE_MOCK_DATA}`);
  
  addTestResult('Configuration', true, 'All required environment variables are set');
  return true;
}

async function testXanoConnection() {
  log('\n🔗 Testing XANO Connection...', 'bold');
  
  if (CONFIG.USE_MOCK_DATA) {
    logWarning('Skipping XANO connection test (using mock data)');
    addTestResult('XANO Connection', true, 'Skipped - using mock data', true);
    return true;
  }
  
  try {
    // Test basic connectivity
    const response = await xanoAPI.get('/health', { timeout: 5000 });
    logSuccess('XANO API is accessible');
    addTestResult('XANO Connection', true, 'Successfully connected to XANO API');
    return true;
  } catch (error) {
    if (error.code === 'ENOTFOUND') {
      logError('XANO API endpoint not found - check URL');
      addTestResult('XANO Connection', false, 'DNS resolution failed');
    } else if (error.code === 'ECONNREFUSED') {
      logError('Connection refused - XANO API may be down');
      addTestResult('XANO Connection', false, 'Connection refused');
    } else if (error.response?.status === 404) {
      logWarning('Health endpoint not found, but XANO is accessible');
      addTestResult('XANO Connection', true, 'XANO accessible (health endpoint not found)', true);
      return true;
    } else {
      logError(`Connection error: ${error.message}`);
      addTestResult('XANO Connection', false, error.message);
    }
    return false;
  }
}

async function validateFileStructure() {
  log('\n📁 Validating File Structure...', 'bold');
  
  const requiredFiles = [
    'src/api/XanoAPIClient.ts',
    'src/services/api.ts',
    'src/config.ts',
    '.env',
    'XANO_INTEGRATION.md'
  ];
  
  let allFilesExist = true;
  
  for (const file of requiredFiles) {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      logSuccess(`Found: ${file}`);
    } else {
      logError(`Missing: ${file}`);
      allFilesExist = false;
    }
  }
  
  addTestResult('File Structure', allFilesExist, 
    allFilesExist ? 'All required files present' : 'Some required files missing');
  
  return allFilesExist;
}

async function validateAPIEndpoints() {
  log('\n🔌 Validating API Endpoints...', 'bold');
  
  if (CONFIG.USE_MOCK_DATA) {
    logWarning('Skipping API endpoint validation (using mock data)');
    addTestResult('API Endpoints', true, 'Skipped - using mock data', true);
    return true;
  }
  
  const endpoints = [
    { path: '/golfers', method: 'GET', name: 'Golfers List' },
    { path: '/tee-times', method: 'GET', name: 'Tee Times' },
    { path: '/products', method: 'GET', name: 'Pro Shop Products' },
    { path: '/menu-items', method: 'GET', name: 'Menu Items' },
    { path: '/staff', method: 'GET', name: 'Staff List' },
    { path: '/analytics', method: 'GET', name: 'Analytics' },
    { path: '/dashboard/metrics', method: 'GET', name: 'Dashboard Metrics' }
  ];
  
  let successCount = 0;
  
  for (const endpoint of endpoints) {
    try {
      const response = await xanoAPI.request({
        method: endpoint.method,
        url: endpoint.path,
        params: { course_id: CONFIG.COURSE_ID }
      });
      
      if (response.status === 200) {
        logSuccess(`${endpoint.name}: OK`);
        successCount++;
      } else {
        logWarning(`${endpoint.name}: Unexpected status ${response.status}`);
      }
    } catch (error) {
      if (error.response?.status === 404) {
        logWarning(`${endpoint.name}: Endpoint not found (404)`);
      } else if (error.response?.status === 401) {
        logWarning(`${endpoint.name}: Authentication required (401)`);
      } else {
        logError(`${endpoint.name}: ${error.message}`);
      }
    }
  }
  
  const allPassed = successCount === endpoints.length;
  addTestResult('API Endpoints', allPassed, 
    `${successCount}/${endpoints.length} endpoints accessible`);
  
  return allPassed;
}

async function generateReport() {
  log('\n📊 Test Results Summary', 'bold');
  log('='.repeat(50), 'blue');
  
  log(`✅ Passed: ${testResults.passed}`, 'green');
  log(`❌ Failed: ${testResults.failed}`, 'red');
  log(`⚠️  Warnings: ${testResults.warnings}`, 'yellow');
  
  const totalTests = testResults.passed + testResults.failed;
  const successRate = totalTests > 0 ? ((testResults.passed / totalTests) * 100).toFixed(1) : 0;
  
  log(`\n📈 Success Rate: ${successRate}%`, successRate >= 80 ? 'green' : 'red');
  
  if (testResults.failed > 0) {
    log('\n❌ Failed Tests:', 'red');
    testResults.tests
      .filter(test => !test.passed && !test.warning)
      .forEach(test => log(`   • ${test.name}: ${test.message}`, 'red'));
  }
  
  if (testResults.warnings > 0) {
    log('\n⚠️  Warnings:', 'yellow');
    testResults.tests
      .filter(test => test.warning)
      .forEach(test => log(`   • ${test.name}: ${test.message}`, 'yellow'));
  }
  
  // Save detailed report
  const reportPath = path.join(process.cwd(), 'integration-validation-report.json');
  fs.writeFileSync(reportPath, JSON.stringify({
    timestamp: new Date().toISOString(),
    configuration: CONFIG,
    results: testResults
  }, null, 2));
  
  log(`\n📄 Detailed report saved to: ${reportPath}`, 'blue');
  
  return testResults.failed === 0;
}

async function main() {
  log('🚀 Starting XANO Integration Validation', 'bold');
  log('='.repeat(50), 'blue');
  
  try {
    const configValid = await validateConfiguration();
    const filesValid = await validateFileStructure();
    const connectionValid = await testXanoConnection();
    const endpointsValid = await validateAPIEndpoints();
    
    const overallSuccess = await generateReport();
    
    if (overallSuccess) {
      log('\n🎉 Integration validation completed successfully!', 'green');
      process.exit(0);
    } else {
      log('\n💥 Integration validation failed. Please check the errors above.', 'red');
      process.exit(1);
    }
    
  } catch (error) {
    logError(`Validation script failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the validation
if (require.main === module) {
  main();
}

module.exports = {
  validateConfiguration,
  testXanoConnection,
  validateFileStructure,
  validateAPIEndpoints
};
