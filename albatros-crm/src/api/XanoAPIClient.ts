import axios, { AxiosInstance, AxiosResponse } from 'axios';
import config from '../config';

// Types for API responses
export interface XanoResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Auth types
export interface LoginResponse {
  authToken: string;
  user: User;
}

export interface User {
  id: number;
  email: string;
  name: string;
  role: string;
  course_id?: number;
  created_at: string;
  updated_at: string;
}

// Golfer types
export interface Golfer {
  id: number;
  first_name: string;
  last_name: string;
  email?: string;
  phone: string;
  address?: string;
  stars: number;
  is_member: boolean;
  last_play_date?: string;
  upcoming_play_date?: string;
  avatar?: string;
  avatar_color?: string;
  course_id: number;
  created_at: string;
  updated_at: string;
}

export interface GolferCreateData {
  first_name: string;
  last_name: string;
  email?: string;
  phone: string;
  address?: string;
  stars?: number;
  is_member?: boolean;
  last_play_date?: string;
  upcoming_play_date?: string;
  avatar?: string;
  avatar_color?: string;
  course_id: number;
}

// Dashboard types
export interface DashboardMetrics {
  total_revenue: number;
  total_bookings: number;
  total_golfers: number;
  member_count: number;
  golfer_return_rate: number;
  obsolete_golfers: number;
  revenue_change: number;
  bookings_change: number;
  golfers_change: number;
}

// Course types
export interface Course {
  id: string;
  name: string;
  location: {
    city: string;
    state: string;
  };
  holes: number;
  par: number;
}

// Weather types
export interface Weather {
  temperature: number;
  conditions: string;
  windSpeed: number;
  precipitation: number;
}

class XanoAPIClient {
  private axiosInstance: AxiosInstance;
  private token: string | null = null;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: config.xanoBaseUrl,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Load token from localStorage
    this.token = localStorage.getItem('xano_token');
    if (this.token) {
      this.setAuthToken(this.token);
    }

    // Add response interceptor for error handling
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.clearAuth();
        }
        return Promise.reject(error);
      }
    );
  }

  private setAuthToken(token: string) {
    this.token = token;
    this.axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    localStorage.setItem('xano_token', token);
  }

  private clearAuth() {
    this.token = null;
    delete this.axiosInstance.defaults.headers.common['Authorization'];
    localStorage.removeItem('xano_token');
  }

  // Auth methods
  async login(email: string, password: string): Promise<LoginResponse> {
    const response = await this.axiosInstance.post('/auth/login', { email, password });
    const { authToken, user } = response.data;
    this.setAuthToken(authToken);
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await this.axiosInstance.post('/auth/logout');
    } finally {
      this.clearAuth();
    }
  }

  async getMe(): Promise<User> {
    const response = await this.axiosInstance.get('/auth/me');
    return response.data;
  }

  // Golfer methods
  async getGolfers(courseId: string, params?: {
    search?: string;
    stars?: number[];
    membership?: string[];
    playStatus?: string[];
    limit?: number;
    offset?: number;
  }): Promise<Golfer[]> {
    const response = await this.axiosInstance.get('/golfers', {
      params: {
        course_id: courseId,
        ...params,
      },
    });
    return response.data;
  }

  async getGolfer(id: number): Promise<Golfer> {
    const response = await this.axiosInstance.get(`/golfers/${id}`);
    return response.data;
  }

  async createGolfer(data: GolferCreateData): Promise<Golfer> {
    const response = await this.axiosInstance.post('/golfers', data);
    return response.data;
  }

  async updateGolfer(id: number, data: Partial<GolferCreateData>): Promise<Golfer> {
    const response = await this.axiosInstance.put(`/golfers/${id}`, data);
    return response.data;
  }

  async deleteGolfer(id: number): Promise<void> {
    await this.axiosInstance.delete(`/golfers/${id}`);
  }

  // Dashboard methods
  async getDashboardMetrics(courseId: string, timeframe: 'daily' | 'weekly' | 'monthly' = 'monthly'): Promise<DashboardMetrics> {
    const response = await this.axiosInstance.get('/dashboard/metrics', {
      params: {
        course_id: courseId,
        timeframe,
      },
    });
    return response.data;
  }

  // Course methods
  async getCourse(courseId: string): Promise<Course> {
    const response = await this.axiosInstance.get(`/courses/${courseId}`);
    return response.data;
  }

  // Weather methods
  async getWeather(courseId: string, date?: string): Promise<Weather> {
    const response = await this.axiosInstance.get('/weather', {
      params: {
        course_id: courseId,
        date,
      },
    });
    return response.data;
  }

  // Tee Time methods
  async getTeeTimes(courseId: string, date?: string, view?: string): Promise<any[]> {
    const response = await this.axiosInstance.get('/tee-times', {
      params: {
        course_id: courseId,
        date,
        view,
      },
    });
    return response.data;
  }

  async createTeeTime(data: any): Promise<any> {
    const response = await this.axiosInstance.post('/tee-times', data);
    return response.data;
  }

  async updateTeeTime(id: number, data: any): Promise<any> {
    const response = await this.axiosInstance.put(`/tee-times/${id}`, data);
    return response.data;
  }

  async deleteTeeTime(id: number): Promise<void> {
    await this.axiosInstance.delete(`/tee-times/${id}`);
  }

  // Pro Shop methods
  async getProducts(courseId: string, params?: {
    category?: string;
    search?: string;
    limit?: number;
    offset?: number;
  }): Promise<any[]> {
    const response = await this.axiosInstance.get('/products', {
      params: {
        course_id: courseId,
        ...params,
      },
    });
    return response.data;
  }

  async createProduct(data: any): Promise<any> {
    const response = await this.axiosInstance.post('/products', data);
    return response.data;
  }

  async updateProduct(id: number, data: any): Promise<any> {
    const response = await this.axiosInstance.put(`/products/${id}`, data);
    return response.data;
  }

  async deleteProduct(id: number): Promise<void> {
    await this.axiosInstance.delete(`/products/${id}`);
  }

  // Food & Beverage methods
  async getMenuItems(courseId: string, params?: {
    category?: string;
    search?: string;
    limit?: number;
    offset?: number;
  }): Promise<any[]> {
    const response = await this.axiosInstance.get('/menu-items', {
      params: {
        course_id: courseId,
        ...params,
      },
    });
    return response.data;
  }

  async createMenuItem(data: any): Promise<any> {
    const response = await this.axiosInstance.post('/menu-items', data);
    return response.data;
  }

  async updateMenuItem(id: number, data: any): Promise<any> {
    const response = await this.axiosInstance.put(`/menu-items/${id}`, data);
    return response.data;
  }

  async deleteMenuItem(id: number): Promise<void> {
    await this.axiosInstance.delete(`/menu-items/${id}`);
  }

  // Orders methods
  async getOrders(courseId: string, params?: {
    type?: 'food' | 'product';
    status?: string;
    date?: string;
    limit?: number;
    offset?: number;
  }): Promise<any[]> {
    const response = await this.axiosInstance.get('/orders', {
      params: {
        course_id: courseId,
        ...params,
      },
    });
    return response.data;
  }

  async createOrder(data: any): Promise<any> {
    const response = await this.axiosInstance.post('/orders', data);
    return response.data;
  }

  async updateOrder(id: number, data: any): Promise<any> {
    const response = await this.axiosInstance.put(`/orders/${id}`, data);
    return response.data;
  }

  // Analytics methods
  async getAnalytics(courseId: string, params?: {
    type?: string;
    timeframe?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<any> {
    const response = await this.axiosInstance.get('/analytics', {
      params: {
        course_id: courseId,
        ...params,
      },
    });
    return response.data;
  }

  // Staff/Back Office methods
  async getStaff(courseId: string): Promise<any[]> {
    const response = await this.axiosInstance.get('/staff', {
      params: { course_id: courseId },
    });
    return response.data;
  }

  async createStaff(data: any): Promise<any> {
    const response = await this.axiosInstance.post('/staff', data);
    return response.data;
  }

  async updateStaff(id: number, data: any): Promise<any> {
    const response = await this.axiosInstance.put(`/staff/${id}`, data);
    return response.data;
  }

  // Generic request method for custom endpoints
  async request<T>(method: 'GET' | 'POST' | 'PUT' | 'DELETE', endpoint: string, data?: any, params?: any): Promise<T> {
    const response = await this.axiosInstance.request({
      method,
      url: endpoint,
      data,
      params,
    });
    return response.data;
  }
}

// Create singleton instance
export const xanoAPI = new XanoAPIClient();
export default xanoAPI;
