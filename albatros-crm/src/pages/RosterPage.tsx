import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  TextField,
  InputAdornment,
  Checkbox,
  Chip,
  styled
} from '@mui/material';
import { Theme } from '@mui/material/styles';
import {
  FilterList as FilterIcon,
  KeyboardArrowDown as ArrowDownIcon,
  Add as AddIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import GolferMetricsCard from '../components/Roster/GolferMetricsCard';
import GolferListItem from '../components/Roster/GolferListItem';
import FilterMenu from '../components/Roster/FilterMenu';
import SortMenu from '../components/Roster/SortMenu';
import GolferFormModal from '../components/Roster/GolferFormModal';
import SelectionToolbar from '../components/Roster/SelectionToolbar';
import { rosterAPI } from '../services/xanoAPI';
import config from '../config';
import {
  <PERSON><PERSON>,
  GolferFormData,
  Filters,
  SortOption,
  RosterPageProps,
} from './types';

const ColumnHeader = styled(Typography)(({ theme }: { theme: Theme }) => ({
  color: theme.palette.text.secondary,
  fontWeight: 500,
  fontSize: '0.875rem',
  textAlign: 'center',
}));

const LeftAlignedHeader = styled(ColumnHeader)({
  textAlign: 'left',
});

const HeaderContainer = styled(Box)(({ theme }: { theme: Theme }) => ({
  display: 'grid',
  gridTemplateColumns: '40px 80px 1fr 1fr 1fr 1fr 1fr',
  gap: theme.spacing(2),
  padding: theme.spacing(2),
  borderBottom: `1px solid ${theme.palette.divider}`,
  alignItems: 'center',
}));

const MonthHeader = styled(Typography)(({ theme }: { theme: Theme }) => ({
  color: theme.palette.text.primary,
  fontWeight: 600,
  fontSize: '1.25rem',
  marginBottom: theme.spacing(2),
}));

const RosterPage: React.FC<RosterPageProps> = ({ courseId }) => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [golfers, setGolfers] = useState<Golfer[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  const [sortAnchorEl, setSortAnchorEl] = useState<null | HTMLElement>(null);
  const [filters, setFilters] = useState<Filters>({
    stars: [],
    membership: [],
    playStatus: [],
  });
  const [currentSort, setCurrentSort] = useState<SortOption | null>(null);
  const [formModalOpen, setFormModalOpen] = useState<boolean>(false);
  const [selectedGolfer, setSelectedGolfer] = useState<GolferFormData | null>(null);
  const [selectedGolfers, setSelectedGolfers] = useState<Set<string>>(new Set());

  // Load golfers data
  const loadGolfers = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await rosterAPI.getGolfers(courseId || config.courseId, {
        search: searchTerm,
        stars: filters.stars,
        membership: filters.membership,
        playStatus: filters.playStatus,
      });

      // Transform XANO data to match local Golfer interface
      const transformedGolfers = response.data.map((xanoGolfer: any) => ({
        id: xanoGolfer.id,
        name: `${xanoGolfer.first_name} ${xanoGolfer.last_name}`,
        phone: xanoGolfer.phone,
        email: xanoGolfer.email,
        address: xanoGolfer.address,
        stars: xanoGolfer.stars,
        isMember: xanoGolfer.is_member,
        lastPlayDate: xanoGolfer.last_play_date || 'Never',
        upcomingPlayDate: xanoGolfer.upcoming_play_date || 'NONE',
        avatar: xanoGolfer.avatar,
        avatarColor: xanoGolfer.avatar_color,
        initials: `${xanoGolfer.first_name?.[0] || ''}${xanoGolfer.last_name?.[0] || ''}`,
      }));
      setGolfers(transformedGolfers);
    } catch (err: any) {
      setError(err.message || 'Failed to load golfers');
      console.error('Error loading golfers:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadGolfers();
  }, [courseId]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadGolfers();
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, filters]);

  const handleFilterClick = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleSortClick = (event: React.MouseEvent<HTMLElement>) => {
    setSortAnchorEl(event.currentTarget);
  };

  const handleFilterChange = (category: keyof Filters, value: Filters[keyof Filters][number]) => {
    setFilters(prev => {
      const currentValues = prev[category] as Array<Filters[keyof Filters][number]>;
      const newValues = currentValues.includes(value)
        ? currentValues.filter((v: Filters[keyof Filters][number]) => v !== value)
        : [...currentValues, value];
      
      return {
        ...prev,
        [category]: newValues,
      };
    });
  };

  const handleSortChange = (sortOption: SortOption) => {
    setCurrentSort(sortOption);
  };

  const handleAddGolfer = () => {
    setSelectedGolfer(null);
    setFormModalOpen(true);
  };

  const handleEditGolfer = (golfer: Golfer) => {
    const [firstName, ...lastNameParts] = golfer.name.split(' ');
    const lastName = lastNameParts.join(' ');
    
    const formData: GolferFormData = {
      id: golfer.id.toString(),
      firstName,
      lastName,
      phone: golfer.phone,
      email: golfer.email || '',
      address: golfer.address || '',
      stars: golfer.stars,
      isMember: golfer.isMember,
      upcomingPlay: golfer.upcomingPlayDate,
      lastSeen: golfer.lastPlayDate,
      albatrossStarScore: golfer.albatrossStarScore || '',
      nps: golfer.nps || '',
      events: golfer.events || '',
      foodDrink: golfer.foodDrink || '',
      student: golfer.student || '',
      avatar: golfer.avatar === null ? undefined : golfer.avatar,
      avatarColor: golfer.avatarColor
    };
    setSelectedGolfer(formData);
    setFormModalOpen(true);
  };

  const handleSubmitGolfer = async (formData: GolferFormData) => {
    setLoading(true);
    setError(null);

    try {
      if (config.useMockData) {
        // Use mock data for development
        if (formData.id) {
          const updatedGolfer = updateGolfer(Number(formData.id), {
            firstName: formData.firstName,
            lastName: formData.lastName,
            phone: formData.phone,
            email: formData.email,
            address: formData.address,
            stars: formData.stars,
            isMember: formData.isMember,
            lastPlayDate: formData.lastSeen,
            upcomingPlayDate: formData.upcomingPlay,
            albatrossStarScore: formData.albatrossStarScore,
            nps: formData.nps,
            events: formData.events,
            foodDrink: formData.foodDrink,
            student: formData.student,
            avatar: formData.avatar,
            avatarColor: formData.avatarColor
          });
          if (updatedGolfer) {
            setGolfers(getGolfers());
          }
        } else {
          addGolfer({
            firstName: formData.firstName,
            lastName: formData.lastName,
            phone: formData.phone,
            email: formData.email,
            stars: formData.stars,
            isMember: formData.isMember,
            lastPlayDate: formData.lastSeen,
            upcomingPlayDate: formData.upcomingPlay,
            avatar: formData.avatar,
            avatarColor: formData.avatarColor
          });
          setGolfers(getGolfers());
        }
      } else {
        // Use XANO API
        const golferData = {
          first_name: formData.firstName,
          last_name: formData.lastName,
          phone: formData.phone,
          email: formData.email,
          address: formData.address,
          stars: formData.stars,
          is_member: formData.isMember,
          last_play_date: formData.lastSeen,
          upcoming_play_date: formData.upcomingPlay,
          avatar: formData.avatar,
          avatar_color: formData.avatarColor,
        };

        if (formData.id) {
          // Update existing golfer
          const response = await rosterAPI.updateGolfer(courseId || config.courseId, formData.id, golferData);
          if (response.status === 200) {
            await loadGolfers(); // Refresh the list
          } else {
            setError('Failed to update golfer');
          }
        } else {
          // Create new golfer
          const response = await rosterAPI.createGolfer(courseId || config.courseId, golferData);
          if (response.status === 201) {
            await loadGolfers(); // Refresh the list
          } else {
            setError('Failed to create golfer');
          }
        }
      }

      // Close modal on success
      setFormModalOpen(false);
      setSelectedGolfer(null);
    } catch (err: any) {
      setError(err.message || 'Failed to save golfer');
      console.error('Error saving golfer:', err);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = (golfers: Golfer[]): Golfer[] => {
    return golfers.filter(golfer => {
      // Filter by stars
      if (filters.stars.length > 0 && !filters.stars.includes(golfer.stars)) {
        return false;
      }

      // Filter by membership
      if (filters.membership.length > 0) {
        const isMember = filters.membership.includes('member');
        const isNonMember = filters.membership.includes('non-member');
        if (isMember && !golfer.isMember) return false;
        if (isNonMember && golfer.isMember) return false;
      }

      // Filter by play status
      if (filters.playStatus.length > 0) {
        const hasUpcoming = filters.playStatus.includes('upcoming');
        const hasRecent = filters.playStatus.includes('recent');
        
        if (hasUpcoming && golfer.upcomingPlayDate === 'NONE') return false;
        
        const today = new Date();
        const lastPlayDate = new Date(golfer.lastPlayDate);
        const isRecent = (today.getTime() - lastPlayDate.getTime()) / (1000 * 60 * 60 * 24) <= 7;
        if (hasRecent && !isRecent) return false;
      }

      return true;
    });
  };

  const applySort = (golfers: Golfer[]): Golfer[] => {
    if (!currentSort) return golfers;

    return [...golfers].sort((a, b) => {
      const { value, direction } = currentSort;
      const multiplier = direction === 'asc' ? 1 : -1;

      switch (value) {
        case 'name':
          return multiplier * a.name.localeCompare(b.name);
        case 'stars':
          return multiplier * (a.stars - b.stars);
        case 'lastPlayDate':
          return multiplier * (new Date(a.lastPlayDate).getTime() - new Date(b.lastPlayDate).getTime());
        default:
          return 0;
      }
    });
  };

  const filteredGolfers = applySort(
    applyFilters(
      golfers.filter(golfer =>
        golfer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        golfer.phone.includes(searchTerm)
      )
    )
  );

  const getCurrentMonth = () => {
    return new Date().toLocaleString('default', { month: 'long' });
  };

  const handleSelectGolfer = (golferId: string) => {
    setSelectedGolfers(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(golferId)) {
        newSelected.delete(golferId);
      } else {
        newSelected.add(golferId);
      }
      return newSelected;
    });
  };

  const handleSelectAll = () => {
    if (selectedGolfers.size === filteredGolfers.length) {
      setSelectedGolfers(new Set());
    } else {
      setSelectedGolfers(new Set(filteredGolfers.map(g => g.id.toString())));
    }
  };

  const handleSendReminder = async (type: string, message: string) => {
    // Here you would implement the actual reminder sending logic
    console.log(`Sending ${type} reminder to ${selectedGolfers.size} golfers:`, message);
    // After sending, clear selection
    setSelectedGolfers(new Set());
  };

  return (
    <Box>
      {/* Error Display */}
      {error && (
        <Box sx={{ mb: 2, p: 2, bgcolor: 'error.light', color: 'error.contrastText', borderRadius: 1 }}>
          <Typography variant="body2">{error}</Typography>
        </Box>
      )}

      {/* Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <GolferMetricsCard
            icon="👥"
            title="Members"
            value={mockMetrics.members.value}
            change={mockMetrics.members.change}
            period="vs last month"
            bgColor="#E1F9F5"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <GolferMetricsCard
            icon="🔄"
            title="Golfer Return Rate"
            value={`${mockMetrics.golferReturnRate.percentage}%`}
            change={mockMetrics.golferReturnRate.change}
            period="vs last month"
            bgColor="#E1F1FE"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <GolferMetricsCard
            icon="⛳"
            title="Obsolete Golfers"
            value={mockMetrics.obsoleteGolfers.value}
            change={mockMetrics.obsoleteGolfers.change}
            period="vs last month"
            bgColor="#FFE9F3"
          />
        </Grid>
      </Grid>

      {/* Golfers Section */}
      <Paper sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6">Golfers</Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<FilterIcon />}
              endIcon={<ArrowDownIcon />}
              onClick={handleFilterClick}
              sx={{ textTransform: 'none' }}
            >
              Filter
            </Button>
            <Button
              variant="outlined"
              endIcon={<ArrowDownIcon />}
              onClick={handleSortClick}
              sx={{ textTransform: 'none' }}
            >
              Sort
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddGolfer}
              sx={{ textTransform: 'none', bgcolor: '#4CAF50' }}
            >
              Add Golfer
            </Button>
          </Box>
        </Box>

        {/* Search Bar */}
        <TextField
          fullWidth
          placeholder="Search golfers..."
          value={searchTerm}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
          sx={{ mb: 3 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />

        {/* Active Filters Display */}
        <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center' }}>
          {filters.stars.length > 0 && (
            <Chip
              label={`${filters.stars.join(', ')} Stars`}
              onDelete={() => filters.stars.forEach(star => handleFilterChange('stars', star))}
              color="primary"
              variant="outlined"
              size="small"
            />
          )}
          {filters.membership.length > 0 && (
            <Chip
              label={filters.membership.join(', ')}
              onDelete={() => filters.membership.forEach(member => handleFilterChange('membership', member))}
              color="primary"
              variant="outlined"
              size="small"
            />
          )}
          {filters.playStatus.length > 0 && (
            <Chip
              label={filters.playStatus.join(', ')}
              onDelete={() => filters.playStatus.forEach(status => handleFilterChange('playStatus', status))}
              color="primary"
              variant="outlined"
              size="small"
            />
          )}
          {currentSort && (
            <Chip
              label={`Sorted by: ${currentSort.value} ${currentSort.direction === 'asc' ? '↑' : '↓'}`}
              onDelete={() => setCurrentSort(null)}
              color="secondary"
              variant="outlined"
              size="small"
            />
          )}
          {filters.stars.length === 0 && filters.membership.length === 0 && 
           filters.playStatus.length === 0 && !currentSort && (
            <Typography variant="body2" color="text.secondary">
              No filters applied
            </Typography>
          )}
        </Box>

        {/* Selection Toolbar */}
        {selectedGolfers.size > 0 && (
          <SelectionToolbar
            selectedCount={selectedGolfers.size}
            onSelectAll={handleSelectAll}
            allSelected={selectedGolfers.size === filteredGolfers.length}
            onSendReminder={handleSendReminder}
          />
        )}

        {/* Month Header */}
        <MonthHeader>{getCurrentMonth()}</MonthHeader>

        {/* Column Headers */}
        <HeaderContainer>
          <Box>
            <Checkbox
              checked={selectedGolfers.size === filteredGolfers.length}
              indeterminate={selectedGolfers.size > 0 && selectedGolfers.size < filteredGolfers.length}
              onChange={handleSelectAll}
              sx={{ p: 0 }}
            />
          </Box>
          <Box /> {/* Space for avatar */}
          <LeftAlignedHeader>Name</LeftAlignedHeader>
          <ColumnHeader>Phone Number</ColumnHeader>
          <ColumnHeader>Stars</ColumnHeader>
          <ColumnHeader>Last Play Date</ColumnHeader>
          <ColumnHeader>Upcoming Play Date</ColumnHeader>
        </HeaderContainer>

        {/* Golfer List */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <Typography variant="body2" color="text.secondary">Loading golfers...</Typography>
          </Box>
        ) : filteredGolfers.length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <Typography variant="body2" color="text.secondary">
              {searchTerm || filters.stars.length > 0 || filters.membership.length > 0 || filters.playStatus.length > 0
                ? 'No golfers match your search criteria'
                : 'No golfers found'
              }
            </Typography>
          </Box>
        ) : (
          filteredGolfers.map((golfer) => (
            <GolferListItem
              key={golfer.id.toString()}
              golfer={{
                id: golfer.id.toString(),
                name: golfer.name,
                phone: golfer.phone,
                stars: golfer.stars,
                isMember: golfer.isMember,
                lastPlayDate: golfer.lastPlayDate,
                upcomingPlayDate: golfer.upcomingPlayDate,
                avatar: golfer.avatar || undefined,
                avatarColor: golfer.avatarColor || undefined,
                initials: golfer.initials || undefined
              }}
              selected={selectedGolfers.has(golfer.id.toString())}
              onSelect={() => handleSelectGolfer(golfer.id.toString())}
              onEdit={() => handleEditGolfer(golfer)}
            />
          ))
        )}

        <FilterMenu
          anchorEl={filterAnchorEl}
          open={Boolean(filterAnchorEl)}
          onClose={() => setFilterAnchorEl(null)}
          filters={filters}
          onFilterChange={handleFilterChange}
        />

        <SortMenu
          anchorEl={sortAnchorEl}
          open={Boolean(sortAnchorEl)}
          onClose={() => setSortAnchorEl(null)}
          currentSort={currentSort}
          onSortChange={handleSortChange}
        />

        <GolferFormModal
          open={formModalOpen}
          onClose={() => setFormModalOpen(false)}
          onSubmit={handleSubmitGolfer}
          golfer={selectedGolfer}
        />
      </Paper>
    </Box>
  );
};

export default RosterPage; 