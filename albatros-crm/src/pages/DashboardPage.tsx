import React, { useState, useEffect } from 'react';
import { Box, Grid, Typography, CircularProgress, Alert } from '@mui/material';
import TeeTimesList from '../components/TeeTimesList';
import Overview from '../components/Overview/Overview';
import SatisfactionDonut from '../components/SatisfactionDonut/SatisfactionDonut';
import { DashboardPageProps } from './types';
import { MetricsCard } from '../components/MetricsCard';
import { dashboardAPI } from '../services/api';
import config from '../config';

const mockData = {
  value: 45,
  percentage: '+2.3%'
};

/**
 * Dashboard page component
 * Displays overview of golf course metrics and activities
 */
const DashboardPage: React.FC<DashboardPageProps> = ({ courseId, timeView, setTimeView }) => {
  const [metrics, setMetrics] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Load dashboard metrics
  const loadMetrics = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await dashboardAPI.getOverview(courseId || config.courseId, timeView);
      if (response.status === 200 && response.data) {
        setMetrics(response.data);
      } else {
        setError('Failed to load dashboard metrics');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load dashboard metrics');
      console.error('Error loading dashboard metrics:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMetrics();
  }, [courseId, timeView]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricsCard
            icon="money"
            label="Earnings"
            value={metrics?.total_revenue ? `$${metrics.total_revenue.toLocaleString()}` : "$45,678"}
            subtitle="Last 30 days"
            change={metrics?.revenue_change || 8}
            colorScheme="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricsCard
            icon="trending_up"
            label="Book Percentage"
            value={metrics?.total_bookings ? `${metrics.total_bookings}%` : "78%"}
            subtitle="Booking rate"
            change={metrics?.bookings_change || 12}
            colorScheme="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricsCard
            icon="store"
            label="Total Golfers"
            value={metrics?.total_golfers ? metrics.total_golfers.toLocaleString() : "1,234"}
            subtitle="This month"
            change={metrics?.golfers_change || 5}
            colorScheme="info"
          />
        </Grid>
      </Grid>
      
      <Grid container spacing={3} sx={{ mt: 3 }}>
        <Grid item xs={12} md={6}>
          <Overview
            data={metrics || mockData}
            onTimeRangeChange={(range) => {
              console.log('Time range changed:', range);
              // You can add logic here to update the time view
            }}
            courseId={courseId}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <SatisfactionDonut courseId={courseId} />
        </Grid>
        <Grid item xs={12}>
          <TeeTimesList courseId={courseId} />
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage; 