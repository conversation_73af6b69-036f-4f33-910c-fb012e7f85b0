import React, { useState, useEffect } from 'react';
import { Box, Grid, Paper, Typography, CircularProgress, Alert } from '@mui/material';
import {
  Group,
  AttachMoney,
  Speed,
  AccessTime,
  TrendingUp
} from '@mui/icons-material';
import { AnalyticsPageProps, AnalyticsData } from './types';
import { analyticsAPI } from '../services/api';
import config from '../config';

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: number;
  color?: string;
  subtitle?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ 
  title, 
  value, 
  icon, 
  trend, 
  color = 'primary', 
  subtitle 
}) => (
  <Paper sx={{ p: 2, height: '100%' }}>
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
      <Box sx={{ 
        backgroundColor: `${color}.light`,
        borderRadius: '50%',
        p: 1,
        mr: 2
      }}>
        {icon}
      </Box>
      <Typography variant="subtitle2" color="text.secondary">
        {title}
      </Typography>
    </Box>
    <Typography variant="h4" sx={{ mb: 1 }}>
      {value}
    </Typography>
    {subtitle && (
      <Typography variant="body2" color="text.secondary">
        {subtitle}
      </Typography>
    )}
  </Paper>
);

const AnalyticsPage: React.FC<AnalyticsPageProps> = ({ courseId }) => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    trends: {
      daily: [12, 8, 15], // Revenue, Members, Return Rate changes
    },
    metrics: {
      members: {
        total: 1250,
        change: 8
      },
      golferReturnRate: {
        percentage: 85,
        change: 15
      },
      obsoleteGolfers: {
        total: 0,
        change: 0
      },
      revenue: {
        total: 250000,
        change: 12
      }
    },
    teeTimeDistribution: [],
    revenueBreakdown: {
      proShop: 0,
      teeTime: 0,
      foodBeverage: 0,
      events: 0,
      other: 0
    }
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Load analytics data
  useEffect(() => {
    const loadAnalytics = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await analyticsAPI.getOverview(courseId || config.courseId, 'monthly');
        if (response.status === 200 && response.data) {
          const data = response.data;
          setAnalyticsData(prev => ({
            ...prev,
            metrics: {
              ...prev.metrics,
              revenue: {
                total: data.revenue || prev.metrics.revenue.total,
                change: data.revenue_change || prev.metrics.revenue.change
              },
              members: {
                total: data.bookings || prev.metrics.members.total,
                change: data.bookings_change || prev.metrics.members.change
              },
              golferReturnRate: {
                percentage: data.utilization || prev.metrics.golferReturnRate.percentage,
                change: data.utilization_change || prev.metrics.golferReturnRate.change
              }
            }
          }));
        }
      } catch (err: any) {
        setError(err.message || 'Failed to load analytics data');
        console.error('Error loading analytics:', err);
      } finally {
        setLoading(false);
      }
    };

    loadAnalytics();
  }, [courseId]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Analytics
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Revenue"
            value={`$${analyticsData.metrics.revenue.total.toLocaleString()}`}
            icon={<AttachMoney color="success" />}
            color="success"
            subtitle="Last 30 days"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Bookings"
            value={analyticsData.metrics.members.total.toLocaleString()}
            icon={<Group color="primary" />}
            subtitle="Total bookings"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Growth"
            value={`${analyticsData.trends.daily[0]}%`}
            icon={<TrendingUp color="info" />}
            color="info"
            subtitle="Month over month"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Utilization"
            value={`${analyticsData.metrics.golferReturnRate.percentage}%`}
            icon={<Speed color="warning" />}
            color="warning"
            subtitle="Average"
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default AnalyticsPage; 