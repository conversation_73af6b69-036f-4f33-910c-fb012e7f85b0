import {
  Add as AddIcon,
  Edit as EditIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import {
  Box,
  Button,
  IconButton,
  Paper,
  styled,
  Tab,
  Tabs,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme,
  Alert,
  CircularProgress
} from '@mui/material';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  CategoryManager,
  mockCategories,
  mockProducts,
  OrdersTab,
  Product,
  ProductActions,
  ProductCategories,
  ProductCategory,
  ProductItemEditor,
  ProductItemSkeleton,
  ProductList,
  ReportsTab
} from '../components/ProShop';
import { proShopAPI } from '../services/api';
import config from '../config';

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  marginBottom: theme.spacing(3),
  '& .MuiTab-root': {
    textTransform: 'none',
    minWidth: 120,
    padding: theme.spacing(2),
    color: theme.palette.text.secondary,
    fontSize: '1rem',
    fontWeight: 500,
    '&.Mui-selected': {
      color: theme.palette.primary.main,
      fontWeight: 600,
    },
  },
}));

const PageContainer = styled(Paper)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  boxShadow: theme.shadows[3],
  margin: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  [theme.breakpoints.up('sm')]: {
    margin: theme.spacing(3),
  },
}));

const ContentContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(4),
  },
}));

const ActionButton = styled(Button)(({ theme }) => ({
  height: '48px',
  borderRadius: theme.shape.borderRadius * 2,
  textTransform: 'none',
  fontWeight: 600,
  padding: theme.spacing(1.5, 3),
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-1px)',
    boxShadow: theme.shadows[2],
  },
}));

interface ProShopPageProps {
  courseId: string;
}

// Cache for storing loaded items by category
const categoryCache = new Map<string, Product[]>();

const ProShopPage: React.FC<ProShopPageProps> = ({ courseId }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [activeTab, setActiveTab] = useState<'products' | 'orders' | 'reports'>('products');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(mockCategories[0]?.id || null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<ProductCategory[]>(mockCategories);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [isItemEditorOpen, setIsItemEditorOpen] = useState<boolean>(false);
  const [isCategoryManagerOpen, setIsCategoryManagerOpen] = useState<boolean>(false);
  const [editingItem, setEditingItem] = useState<Product | undefined>(undefined);

  // Memoize filtered items to prevent unnecessary recalculations
  const filteredItems = useMemo(() => {
    if (!searchTerm) return products;
    
    const searchLower = searchTerm.toLowerCase();
    return products.filter(item =>
      item.name.toLowerCase().includes(searchLower) ||
      item.description.toLowerCase().includes(searchLower) ||
      item.brand?.toLowerCase().includes(searchLower)
    );
  }, [products, searchTerm]);

  const loadProducts = useCallback(async (categoryId: string | null) => {
    if (!categoryId) {
      setProducts([]);
      setLoading(false);
      return;
    }

    // Check cache first
    if (categoryCache.has(categoryId)) {
      setProducts(categoryCache.get(categoryId)!);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (config.useMockData) {
        // Use mock data for development
        await new Promise(resolve => setTimeout(resolve, 500));
        const category = categories.find(cat => cat.id === categoryId);
        const items = mockProducts.filter(item => item.category === category?.name);
        categoryCache.set(categoryId, items);
        setProducts(items);
      } else {
        // Use XANO API
        const category = categories.find(cat => cat.id === categoryId);
        const response = await proShopAPI.getInventory(courseId || config.courseId, {
          category: category?.name,
          search: searchTerm,
        });

        if (response.status === 200 && response.data) {
          // Transform XANO data to match local Product interface
          const transformedProducts = response.data.map((xanoProduct: any) => ({
            id: xanoProduct.id,
            name: xanoProduct.name || xanoProduct.pro_shop_item_name,
            description: xanoProduct.description || '',
            price: xanoProduct.price || 0,
            category: xanoProduct.category || xanoProduct.pro_shop_category_name,
            imageUrl: xanoProduct.image_url,
            inStock: xanoProduct.inventory_in_stock > 0,
            quantity: xanoProduct.inventory_in_stock || 0,
            brand: xanoProduct.brand,
            size: xanoProduct.size,
            color: xanoProduct.color,
            mobileApp: xanoProduct.mobile_app || false,
            order: xanoProduct.order || 0,
          }));
          categoryCache.set(categoryId, transformedProducts);
          setProducts(transformedProducts);
        } else {
          setError('Failed to load products');
        }
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      console.error('Error loading products:', err);
    } finally {
      setLoading(false);
    }
  }, [categories, courseId, searchTerm]);

  useEffect(() => {
    loadProducts(selectedCategory);
  }, [selectedCategory, loadProducts]);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const handleMobileAppToggle = async (itemId: string, currentValue: boolean) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setProducts(prevItems =>
        prevItems.map(item =>
          item.id === itemId ? { ...item, mobileApp: !currentValue } : item
        )
      );
      // Update cache
      if (selectedCategory) {
        categoryCache.set(selectedCategory, products);
      }
    } catch (err) {
      setError('Failed to update mobile app status');
    }
  };

  const handleEditItem = (item: Product) => {
    setEditingItem(item);
    setIsItemEditorOpen(true);
  };

  const handleAddItem = () => {
    setEditingItem(undefined);
    setIsItemEditorOpen(true);
  };

  const handleSaveItem = (item: Product) => {
    if (editingItem) {
      // If the category has changed, we need to update both categories
      if (editingItem.category !== item.category) {
        // Remove the item from the old category's cache
        const oldCategoryItems = categoryCache.get(editingItem.category) || [];
        categoryCache.set(
          editingItem.category,
          oldCategoryItems.filter(i => i.id !== item.id)
        );

        // Add the item to the new category's cache
        const newCategoryItems = categoryCache.get(item.category) || [];
        categoryCache.set(
          item.category,
          [...newCategoryItems, item]
        );
      } else {
        // Update the item in the current category's cache
        const categoryItems = categoryCache.get(item.category) || [];
        categoryCache.set(
          item.category,
          categoryItems.map(i => i.id === item.id ? item : i)
        );
      }
    } else {
      // Add new item to the category's cache
      const categoryItems = categoryCache.get(item.category) || [];
      categoryCache.set(
        item.category,
        [...categoryItems, item]
      );
    }

    // Update the products state
    setProducts(prevItems => {
      if (editingItem) {
        return prevItems.map(i => i.id === item.id ? item : i);
      } else {
        return [...prevItems, item];
      }
    });

    setIsItemEditorOpen(false);
    setEditingItem(undefined);
  };

  const handleSaveCategories = (updatedCategories: ProductCategory[]) => {
    setCategories(updatedCategories);
  };

  const handleDeleteItem = (item: Product) => {
    // Remove from current display
    setProducts(prevItems => prevItems.filter(i => i.id !== item.id));
    
    // Remove from cache
    const categoryItems = categoryCache.get(item.category) || [];
    categoryCache.set(
      item.category,
      categoryItems.filter(i => i.id !== item.id)
    );
  };


  const handleReorderCategories = (newCategories: ProductCategory[]) => {
    setCategories(newCategories);
  };

  if (error) {
    return (
      <PageContainer>
        <ContentContainer>
          <Typography color="error" variant="h6" sx={{ mb: 2 }}>
            Error: {error}
          </Typography>
        </ContentContainer>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <ContentContainer>
        {/* Top Navigation */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          mb: 2,
          borderBottom: '1px solid',
          borderColor: 'divider',
        }}>
          <StyledTabs 
            value={activeTab} 
            onChange={(_, newValue) => setActiveTab(newValue as 'products' | 'orders' | 'reports')}
            variant={isMobile ? "fullWidth" : "standard"}
            centered={!isMobile}
          >
            <Tab label="Products" value="products" />
            <Tab label="Orders" value="orders" />
            <Tab label="Reports" value="reports" />
          </StyledTabs>
          {activeTab === 'products' && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Tooltip title="Open Checkout">
                <ActionButton
                  variant="contained"
                  color="primary"
                  onClick={() => window.open('/pro-shop-pos', '_blank')}
                  sx={{ minWidth: 'auto', px: 2 }}
                >
                  Checkout
                </ActionButton>
              </Tooltip>
              <Tooltip title="Manage Categories">
                <IconButton onClick={() => setIsCategoryManagerOpen(true)}>
                  <SettingsIcon />
                </IconButton>
              </Tooltip>
            </Box>
          )}
        </Box>

        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {activeTab === 'products' && (
          <>
            <Box sx={{
              display: 'flex',
              justifyContent: 'space-between',
              gap: 2,
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: { xs: 'stretch', sm: 'center' },
              mt: 3,
              mb: 3,
            }}>
              <Box sx={{ flex: 1 }}>
                <ProductActions
                  onSearch={handleSearch}
                  searchTerm={searchTerm}
                />
              </Box>
              <Box sx={{ 
                display: 'flex', 
                gap: 1,
                flexShrink: 0,
              }}>
                <Tooltip title={isEditMode ? 'Exit Edit Mode' : 'Enter Edit Mode'}>
                  <ActionButton
                    variant={isEditMode ? 'contained' : 'outlined'}
                    color={isEditMode ? 'primary' : 'inherit'}
                    onClick={() => setIsEditMode(!isEditMode)}
                    startIcon={<EditIcon />}
                  >
                    {isEditMode ? 'Save Changes' : 'Edit Inventory'}
                  </ActionButton>
                </Tooltip>
                <Tooltip title="Add New Item">
                  <ActionButton
                    variant="contained"
                    color="primary"
                    onClick={handleAddItem}
                    startIcon={<AddIcon />}
                  >
                    Add Item
                  </ActionButton>
                </Tooltip>
              </Box>
            </Box>

            <ProductCategories
              categories={categories}
              selectedCategory={selectedCategory}
              onCategoryChange={setSelectedCategory}
              isEditMode={isEditMode}
              onReorderCategories={handleReorderCategories}
            />

            {loading ? (
              <Box sx={{ mt: 3 }}>
                <ProductItemSkeleton />
              </Box>
            ) : (
              <ProductList
                items={filteredItems}
                onMobileAppToggle={handleMobileAppToggle}
                onEditItem={handleEditItem}
                onDeleteItem={isEditMode ? handleDeleteItem : undefined}
                isEditMode={isEditMode}
              />
            )}

            <ProductItemEditor
              open={isItemEditorOpen}
              onClose={() => {
                setIsItemEditorOpen(false);
                setEditingItem(undefined);
              }}
              onSave={handleSaveItem}
              categories={categories}
              item={editingItem}
              isEditMode={isEditMode}
              onCategoriesChange={handleSaveCategories}
            />

            <CategoryManager
              open={isCategoryManagerOpen}
              onClose={() => setIsCategoryManagerOpen(false)}
              onSave={handleSaveCategories}
              categories={categories}
            />
          </>
        )}

        {activeTab === 'orders' && (
          <OrdersTab />
        )}

        {activeTab === 'reports' && (
          <ReportsTab />
        )}
      </ContentContainer>
    </PageContainer>
  );
};

export default ProShopPage; 