import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  ToggleButtonGroup,
  ToggleButton,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  KeyboardArrowDown as ArrowDownIcon,
  MoreVert as MoreIcon,
  Check as CheckIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { backOfficeAPI } from '../services/api';
import config from '../config';
import {
  BackOfficePageProps,
  BackOfficeData,
  EmployeeShift,
  DepartmentPayroll,
} from './types';

const BackOfficePage: React.FC<BackOfficePageProps> = ({ courseId }) => {
  const [timeView, setTimeView] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [backOfficeData, setBackOfficeData] = useState<BackOfficeData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        // Format date as YYYY-MM-DDThh:mm:ssZ (RFC3339 format)
        const date = new Date().toISOString().split('.')[0] + 'Z';
        // Use courseId, date, and timeView for the API call
        const response = await backOfficeAPI.getOverview(courseId || config.courseId, date, timeView);
        if (response.status === 200 && response.data) {
          setBackOfficeData(response.data);
        } else {
          setError('Failed to load back office data');
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred');
        console.error('Error loading back office data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [courseId, timeView]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          {error}
        </Alert>
      </Box>
    );
  }

  const getStatusColor = (status: EmployeeShift['status']): 'success' | 'info' | 'default' => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'completed':
        return 'success';
      case 'upcoming':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      {/* Shifts Section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box>
            <Typography variant="h6">Employee Shifts</Typography>
            <Typography variant="body2" color="text.secondary">
              {backOfficeData?.payroll?.currentPeriod?.start ?? 'N/A'} - {backOfficeData?.payroll?.currentPeriod?.end ?? 'N/A'}
            </Typography>
          </Box>
          <Box>
            <ToggleButtonGroup
              value={timeView}
              exclusive
              onChange={(e, value) => value && setTimeView(value)}
              size="small"
            >
              <ToggleButton value="daily">Daily</ToggleButton>
              <ToggleButton value="weekly">Weekly</ToggleButton>
              <ToggleButton value="monthly">Monthly</ToggleButton>
            </ToggleButtonGroup>
            <Button
              endIcon={<ArrowDownIcon />}
              sx={{ textTransform: 'none', ml: 2 }}
            >
              Export to Payroll
            </Button>
          </Box>
        </Box>

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Employee</TableCell>
                <TableCell>Department</TableCell>
                <TableCell>Clock In</TableCell>
                <TableCell>Clock Out</TableCell>
                <TableCell>Hours</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {backOfficeData?.shifts?.map((shift) => (
                <TableRow key={shift.id}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar 
                        src={shift.avatar} 
                        sx={{ width: 32, height: 32, mr: 2, bgcolor: 'primary.main' }}
                      >
                        {shift.employeeName.charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle2">{shift.employeeName}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          {shift.role}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>{shift.department}</TableCell>
                  <TableCell>{new Date(shift.clockIn).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</TableCell>
                  <TableCell>
                    {shift.clockOut ? new Date(shift.clockOut).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : '-'}
                  </TableCell>
                  <TableCell>{shift.hoursWorked ? `${shift.hoursWorked} hrs` : '-'}</TableCell>
                  <TableCell>
                    <Chip
                      label={shift.status}
                      color={getStatusColor(shift.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="right">
                    {shift.status === 'active' ? (
                      <Box>
                        <IconButton color="success" size="small">
                          <CheckIcon />
                        </IconButton>
                        <IconButton color="error" size="small">
                          <CloseIcon />
                        </IconButton>
                      </Box>
                    ) : (
                      <IconButton size="small">
                        <MoreIcon />
                      </IconButton>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Payroll Summary */}
        <Box sx={{ mt: 3, pt: 3, borderTop: 1, borderColor: 'divider' }}>
          <Grid container spacing={3}>
            {backOfficeData?.payroll?.currentPeriod?.departments?.map((dept: DepartmentPayroll) => (
              <Grid item xs={12} md={3} key={dept.name}>
                <Box>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {dept.name}
                  </Typography>
                  <Typography variant="h6">
                    ${dept.amount.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {dept.hours} hrs • {dept.employeeCount} employees
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Paper>

      {/* Marketing Templates Section */}
      <Paper sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6">Marketing Templates</Typography>
          <Button
            variant="contained"
            sx={{ textTransform: 'none' }}
          >
            Create Template
          </Button>
        </Box>

        <Grid container spacing={2}>
          {backOfficeData?.marketingTemplates?.map((template) => (
            <Grid item xs={12} md={6} key={template.id}>
              <Paper 
                sx={{ 
                  p: 2,
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  bgcolor: 'background.default',
                }}
              >
                <Box>
                  <Typography variant="subtitle2">{template.name}</Typography>
                  <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                    <Chip
                      label={template.type}
                      size="small"
                      sx={{ bgcolor: template.type === 'Email' ? '#E1F9F5' : '#FFE9F3' }}
                    />
                    <Typography variant="body2" color="text.secondary">
                      Last modified: {new Date(template.lastModified).toLocaleDateString()}
                    </Typography>
                  </Box>
                  {template.performance && (
                    <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>
                      <Typography variant="caption" color="text.secondary">
                        Opens: {template.performance.opens}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Clicks: {template.performance.clicks}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Conversions: {template.performance.conversions}
                      </Typography>
                    </Box>
                  )}
                </Box>
                <IconButton size="small">
                  <MoreIcon />
                </IconButton>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Paper>
    </Box>
  );
};

export default BackOfficePage; 