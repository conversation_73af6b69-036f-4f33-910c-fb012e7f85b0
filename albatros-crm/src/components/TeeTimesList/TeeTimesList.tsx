import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Grid,
  Typography,
  Paper,
  styled,
  Alert,
  CircularProgress,
} from '@mui/material';
import { TeeTimeWeeklyList } from './TeeTimeWeeklyList';
import { TeeTimeMonthlyList } from './TeeTimeMonthlyList';
import { TeeTimesListProps, TeeTimeData, ViewType } from './types';
import { ViewToggle } from './ViewToggle';
import { TeeTimeDailyList } from './TeeTimeDailyList';
import { generateMockDataForDays } from './utils';
import { teeTimesAPI } from '../../services/api';
import config from '../../config';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[1],
}));

const ViewHeader = styled(Box)(({ theme }) => ({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
  width: '100%'
}));

const ViewToggleContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  left: '50%',
  transform: 'translateX(-50%)',
  display: 'flex',
  justifyContent: 'center',
  zIndex: 1
}));

// Update TeeTimeData interface to include revenue and utilization
interface ExtendedTeeTimeData extends TeeTimeData {
  revenue: number;
  utilization: number;
}

export const TeeTimesList: React.FC<TeeTimesListProps> = ({ 
  courseId,
  timeView = 'daily',
  onTeeTimeSelect,
  onSlotClick 
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentView, setCurrentView] = useState<ViewType>(timeView);
  const [data, setData] = useState<ExtendedTeeTimeData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        if (config.useMockData) {
          // Use mock data for development
          const mockData = generateMockDataForDays(30) as ExtendedTeeTimeData[];
          setData(mockData);
        } else {
          // Use XANO API
          const today = new Date().toISOString().split('T')[0];
          const response = await teeTimesAPI.getTeeTimesList(
            courseId || config.courseId,
            currentView,
            today
          );

          if (response.status === 200 && response.data) {
            // Transform XANO data to match local interface
            const transformedData = response.data.map((teeTime: any) => ({
              id: teeTime.id,
              date: teeTime.date || today,
              time: teeTime.time,
              playerName: teeTime.player_name || teeTime.golfer_name,
              numberOfPlayers: teeTime.number_of_players || 1,
              status: teeTime.status || 'available',
              price: teeTime.price || 0,
              notes: teeTime.notes || '',
            }));
            setData(transformedData);
          } else {
            setError('Failed to load tee times');
          }
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch tee time data');
        console.error('Error fetching tee times:', err);
      } finally {
        setLoading(false);
        // Scroll after data loads
        setTimeout(() => {
          containerRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 100);
      }
    };

    fetchData();
  }, [courseId, currentView]);

  const handleViewChange = (view: ViewType) => {
    setCurrentView(view);
    setTimeout(() => {
      containerRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }, 100);
  };

  const handleDailySlotClick = useCallback((slot: any) => {
    console.log('Daily slot clicked:', slot);
    if (onSlotClick) {
      onSlotClick(slot);
    }
  }, [onSlotClick]);

  const handleWeeklyMonthlySlotClick = useCallback((date: string, time: string) => {
    console.log('Weekly/Monthly slot clicked:', { date, time });
    // Convert to the format expected by the parent component
    const slot = {
      time,
      date,
      // Add any other required properties
    };
    if (onSlotClick) {
      onSlotClick(slot);
    }
  }, [onSlotClick]);

  const renderView = () => {
    switch (currentView) {
      case 'daily':
        return (
          <TeeTimeDailyList
            data={data}
            date={new Date()}
            courseId={courseId}
            onSlotClick={handleDailySlotClick}
          />
        );
      case 'weekly':
        return (
          <TeeTimeWeeklyList
            data={data}
            onSlotClick={handleWeeklyMonthlySlotClick}
            courseId={courseId}
            startDate={new Date()}
            endDate={new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)}
          />
        );
      case 'monthly':
        return (
          <TeeTimeMonthlyList
            data={data}
            onSlotClick={handleWeeklyMonthlySlotClick}
            courseId={courseId}
            month={new Date()}
          />
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  if (error) {
    return <Typography color="error">{error}</Typography>;
  }

  return (
    <Box ref={containerRef}>
      <Grid container spacing={3}>
        <Grid item xs={12} sx={{ mt: 4 }}>
          <ViewHeader>
            <Typography variant="h5" sx={{ zIndex: 2 }}>Tee Times</Typography>
            <ViewToggleContainer>
              <ViewToggle
                currentView={currentView}
                onViewChange={handleViewChange}
                size="small"
              />
            </ViewToggleContainer>
          </ViewHeader>
          <StyledPaper>
            {renderView()}
          </StyledPaper>
        </Grid>
      </Grid>
    </Box>
  );
};
export default TeeTimesList;
