import React, { useState } from 'react';
import {
  Box,
  Switch,
  FormControlLabel,
  Typography,
  Alert,
  Collapse,
  IconButton,
  Paper,
} from '@mui/material';
import { Settings as SettingsIcon, Close as CloseIcon } from '@mui/icons-material';

interface DataSourceToggleProps {
  onToggle?: (useXano: boolean) => void;
}

/**
 * Development component to toggle between mock data and XANO backend
 * This should only be visible in development mode
 */
export const DataSourceToggle: React.FC<DataSourceToggleProps> = ({ onToggle }) => {
  const [useXano, setUseXano] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  const handleToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.checked;
    setUseXano(newValue);
    onToggle?.(newValue);
    
    // Show warning when enabling XANO
    if (newValue) {
      console.warn('Switched to XANO backend - ensure endpoints are configured correctly');
    }
  };

  return (
    <Box sx={{ position: 'fixed', top: 16, right: 16, zIndex: 9999 }}>
      <IconButton
        onClick={() => setShowSettings(!showSettings)}
        sx={{
          bgcolor: 'background.paper',
          boxShadow: 2,
          '&:hover': { bgcolor: 'background.paper' }
        }}
      >
        <SettingsIcon />
      </IconButton>
      
      <Collapse in={showSettings}>
        <Paper sx={{ mt: 1, p: 2, minWidth: 300 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Data Source</Typography>
            <IconButton size="small" onClick={() => setShowSettings(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
          
          <FormControlLabel
            control={
              <Switch
                checked={useXano}
                onChange={handleToggle}
                color="primary"
              />
            }
            label={useXano ? 'XANO Backend' : 'Mock Data'}
          />
          
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Current: {useXano ? 'Real XANO API' : 'Mock Data'}
          </Typography>
          
          {useXano && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              <Typography variant="body2">
                XANO backend enabled. Ensure all endpoints are properly configured.
                Check browser console for API errors.
              </Typography>
            </Alert>
          )}
          
          {!useXano && (
            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                Using mock data for development. All changes are temporary.
              </Typography>
            </Alert>
          )}
        </Paper>
      </Collapse>
    </Box>
  );
};

export default DataSourceToggle;
