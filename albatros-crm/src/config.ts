// Application configuration
interface Config {
  courseId: string;
  apiBaseUrl: string;
  xanoBaseUrl: string;
  defaultTimeView: string;
  useMockData: boolean;
}

const config: Config = {
  courseId: process.env.REACT_APP_COURSE_ID || '6793f989213768ac24c381e4',
  apiBaseUrl: process.env.REACT_APP_API_BASE_URL || 'http://localhost:3000',
  xanoBaseUrl: process.env.REACT_APP_XANO_BASE_URL || 'https://x8ki-letl-twmt.n7.xano.io/api:uP98JIiJ',
  defaultTimeView: 'daily',
  useMockData: process.env.REACT_APP_USE_MOCK_DATA === 'true' || false,
};

export default config;