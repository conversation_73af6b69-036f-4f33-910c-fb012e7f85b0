import axios, { AxiosInstance, InternalAxiosRequestConfig } from 'axios';
import config from '../config';
import { getGolfers, addGolfer, updateGolfer, mockMetrics } from './mockData';
import xanoAPI from '../api/XanoAPIClient';

// For development purposes only
const DEV_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkZXZlbG9wbWVudCIsImlhdCI6MTUxNjIzOTAyMn0.your_signed_token';

// Use configuration to determine mock data usage
// For now, always use mock data until XANO endpoints are properly configured
const useMockData = true; // config.useMockData;

const api: AxiosInstance = axios.create({
  baseURL: config.apiBaseUrl,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${DEV_TOKEN}`
  },
});

// Add auth token to requests if it exists in localStorage, otherwise use mock token
api.interceptors.request.use((config: InternalAxiosRequestConfig) => {
  const token = localStorage.getItem('jwt_token') || DEV_TOKEN;
  config.headers.Authorization = `Bearer ${token}`;
  return config;
});

// API Response Types
interface ApiResponse<T> {
  data: T;
  status: number;
  statusText: string;
}

// Dashboard Types
interface DashboardOverview {
  totalRevenue: number;
  totalBookings: number;
  averageRating: number;
  weather: string;
}

// Tee Times Types
interface TeeTime {
  id: string;
  time: string;
  playerName: string;
  numberOfPlayers: number;
  status: 'confirmed' | 'pending' | 'cancelled';
  notes?: string;
}

// Weather Types
interface Weather {
  temperature: number;
  conditions: string;
  windSpeed: number;
  precipitation: number;
}

// Course Types
interface CourseInfo {
  id: string;
  name: string;
  location?: {
    city: string;
    state: string;
  };
  holes?: number;
  par?: number;
}

// Golfer Types
interface Golfer {
  id: string;
  name: string;
  handicap: number;
  membershipType: string;
}

// Pro Shop Types
interface InventoryItem {
  id: string;
  name: string;
  sku?: string;
  description?: string;
  image?: string;
  cost: number;
  price: number;
  quantity: number;
  category: string;
  mobileApp?: boolean;
  specifications?: {
    clubType?: string;
    brand?: string;
    model?: string;
    material?: string;
    flex?: string;
    loft?: string;
    shaft?: string;
  };
  sizes?: string[];
  colors?: string[];
  membershipDiscount?: number;
  reorderPoint?: number;
  supplier?: string;
  lastOrdered?: string;
  nextOrder?: string;
}

// Nineteenth Hole Types
interface MenuItem {
  id: string;
  name: string;
  price: number;
  category: string;
  description: string;
}

// Analytics Types
interface AnalyticsOverview {
  revenue: number;
  bookings: number;
  utilization: number;
  trends: {
    daily: number[];
    weekly: number[];
    monthly: number[];
  };
}

// Back Office Types
interface EmployeeShift {
  id: string;
  employeeName: string;
  role: string;
  avatar?: string;
  department: string;
  clockIn: string;
  clockOut?: string;
  hoursWorked?: number;
  status: 'active' | 'completed' | 'upcoming';
}

interface DepartmentPayroll {
  name: string;
  amount: number;
  hours: number;
  employeeCount: number;
}

interface PayrollPeriod {
  start: string;
  end: string;
  departments: DepartmentPayroll[];
}

interface MarketingTemplatePerformance {
  opens: number;
  clicks: number;
  conversions: number;
}

interface MarketingTemplate {
  id: string;
  name: string;
  type: 'Email' | 'SMS';
  lastModified: string;
  performance?: MarketingTemplatePerformance;
}

interface BackOfficeData {
  shifts: EmployeeShift[];
  payroll: {
    currentPeriod: PayrollPeriod;
  };
  marketingTemplates: MarketingTemplate[];
}

// Mock API Methods
const mockAPI = {
  getGolfers: async (): Promise<ApiResponse<any[]>> => {
    return {
      data: getGolfers(),
      status: 200,
      statusText: 'OK'
    };
  },
  addGolfer: async (data: any): Promise<ApiResponse<any>> => {
    const newGolfer = addGolfer(data);
    return {
      data: newGolfer,
      status: 201,
      statusText: 'Created'
    };
  },
  updateGolfer: async (id: number, data: any): Promise<ApiResponse<any>> => {
    const updatedGolfer = updateGolfer(id, data);
    return {
      data: updatedGolfer,
      status: 200,
      statusText: 'OK'
    };
  },
  getMetrics: async (): Promise<ApiResponse<any>> => {
    return {
      data: mockMetrics,
      status: 200,
      statusText: 'OK'
    };
  },
  getCourseInfo: async (): Promise<ApiResponse<CourseInfo>> => {
    return {
      data: {
        id: '6793f989213768ac24c381e4',
        name: 'Albatross Golf Club',
        location: {
          city: 'San Francisco',
          state: 'CA'
        },
        holes: 18,
        par: 72
      },
      status: 200,
      statusText: 'OK'
    };
  },
  getWeather: async (): Promise<ApiResponse<Weather>> => {
    return {
      data: {
        temperature: 72,
        conditions: 'Sunny',
        windSpeed: 5,
        precipitation: 0
      },
      status: 200,
      statusText: 'OK'
    };
  }
};

// API Methods
export const dashboardAPI = {
  getOverview: async (courseId: string, timeframe: 'daily' | 'weekly' | 'monthly' = 'monthly'): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return mockAPI.getMetrics();
    }
    try {
      const data = await xanoAPI.getDashboardMetrics(courseId, timeframe);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.warn('Dashboard API Error - falling back to mock data:', error.message);
      // Fallback to mock data if XANO fails
      return mockAPI.getMetrics();
    }
  },
};

export const teeTimesAPI = {
  getTeeTimesList: async (courseId: string, view: 'daily' | 'weekly' | 'monthly' = 'daily', date: string): Promise<ApiResponse<any[]>> => {
    if (useMockData) {
      // Return mock tee times data
      return {
        data: [
          {
            id: '1',
            time: '08:00 AM',
            playerName: 'John Doe',
            numberOfPlayers: 4,
            status: 'confirmed'
          },
          {
            id: '2',
            time: '09:00 AM',
            playerName: 'Jane Smith',
            numberOfPlayers: 2,
            status: 'pending'
          }
        ],
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      const data = await xanoAPI.getTeeTimes(courseId, date, view);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: [],
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },

  createTeeTime: async (courseId: string, data: any): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return {
        data: { id: Date.now().toString(), ...data },
        status: 201,
        statusText: 'Created'
      };
    }
    try {
      const result = await xanoAPI.createTeeTime({ ...data, course_id: parseInt(courseId) });
      return {
        data: result,
        status: 201,
        statusText: 'Created'
      };
    } catch (error: any) {
      return {
        data: null,
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },

  updateTeeTime: async (courseId: string, teeTimeId: string, data: any): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return {
        data: { id: teeTimeId, ...data },
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      const result = await xanoAPI.updateTeeTime(parseInt(teeTimeId), data);
      return {
        data: result,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: null,
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },

  deleteTeeTime: async (courseId: string, teeTimeId: string): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return {
        data: { success: true },
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      await xanoAPI.deleteTeeTime(parseInt(teeTimeId));
      return {
        data: { success: true },
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: { success: false },
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },
};

export const weatherAPI = {
  getWeather: async (courseId: string, date: string): Promise<ApiResponse<Weather>> => {
    if (useMockData) {
      return mockAPI.getWeather();
    }
    try {
      const data = await xanoAPI.getWeather(courseId, date);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return mockAPI.getWeather();
    }
  },
};

export const courseAPI = {
  getCourseInfo: async (courseId: string): Promise<ApiResponse<CourseInfo>> => {
    if (useMockData) {
      return mockAPI.getCourseInfo();
    }
    try {
      const data = await xanoAPI.getCourse(courseId);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return mockAPI.getCourseInfo();
    }
  },
};

export const rosterAPI = {
  getGolfers: async (courseId: string, params: Record<string, any> = {}): Promise<ApiResponse<any[]>> => {
    if (useMockData) {
      return mockAPI.getGolfers();
    }
    try {
      const data = await xanoAPI.getGolfers(courseId, params);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.warn('Roster API Error - falling back to mock data:', error.message);
      // Fallback to mock data if XANO fails
      return mockAPI.getGolfers();
    }
  },
  createGolfer: async (courseId: string, data: any): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return mockAPI.addGolfer(data);
    }
    try {
      const result = await xanoAPI.createGolfer({ ...data, course_id: parseInt(courseId) });
      return {
        data: result,
        status: 201,
        statusText: 'Created'
      };
    } catch (error: any) {
      return {
        data: null,
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },
  updateGolfer: async (courseId: string, golferId: string, data: any): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return mockAPI.updateGolfer(parseInt(golferId), data);
    }
    try {
      const result = await xanoAPI.updateGolfer(parseInt(golferId), data);
      return {
        data: result,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: null,
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },
  deleteGolfer: async (golferId: string): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return {
        data: { success: true },
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      await xanoAPI.deleteGolfer(parseInt(golferId));
      return {
        data: { success: true },
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: { success: false },
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },
  getMetrics: async (courseId: string): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return {
        data: mockMetrics,
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      const data = await xanoAPI.getDashboardMetrics(courseId);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: mockMetrics,
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },
};

export const proShopAPI = {
  getInventory: async (courseId: string, params: Record<string, any> = {}): Promise<ApiResponse<any[]>> => {
    if (useMockData) {
      // Return mock inventory data
      return {
        data: [
          {
            id: '1',
            name: 'Golf Ball Set',
            category: 'Equipment',
            price: 29.99,
            quantity: 50,
            inStock: true
          }
        ],
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      const data = await xanoAPI.getProducts(courseId, params);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: [],
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },

  createItem: async (courseId: string, data: any): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return {
        data: { id: Date.now().toString(), ...data },
        status: 201,
        statusText: 'Created'
      };
    }
    try {
      const result = await xanoAPI.createProduct({ ...data, course_id: parseInt(courseId) });
      return {
        data: result,
        status: 201,
        statusText: 'Created'
      };
    } catch (error: any) {
      return {
        data: null,
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },

  updateItem: async (courseId: string, itemId: string, data: any): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return {
        data: { id: itemId, ...data },
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      const result = await xanoAPI.updateProduct(parseInt(itemId), data);
      return {
        data: result,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: null,
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },

  deleteItem: async (courseId: string, itemId: string): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return {
        data: { success: true },
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      await xanoAPI.deleteProduct(parseInt(itemId));
      return {
        data: { success: true },
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: { success: false },
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },
};

export const nineteenthHoleAPI = {
  getMenu: async (courseId: string, params: Record<string, any> = {}): Promise<ApiResponse<any[]>> => {
    if (useMockData) {
      // Return mock menu data
      return {
        data: [
          {
            id: '1',
            name: 'Club Sandwich',
            description: 'Classic club sandwich with turkey, bacon, lettuce, and tomato',
            price: 12.99,
            category: 'sandwiches',
            isAvailable: true
          }
        ],
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      const data = await xanoAPI.getMenuItems(courseId, params);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: [],
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },

  createMenuItem: async (courseId: string, data: any): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return {
        data: { id: Date.now().toString(), ...data },
        status: 201,
        statusText: 'Created'
      };
    }
    try {
      const result = await xanoAPI.createMenuItem({ ...data, course_id: parseInt(courseId) });
      return {
        data: result,
        status: 201,
        statusText: 'Created'
      };
    } catch (error: any) {
      return {
        data: null,
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },

  updateMenuItem: async (courseId: string, itemId: string, data: any): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return {
        data: { id: itemId, ...data },
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      const result = await xanoAPI.updateMenuItem(parseInt(itemId), data);
      return {
        data: result,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: null,
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },

  deleteMenuItem: async (courseId: string, itemId: string): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return {
        data: { success: true },
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      await xanoAPI.deleteMenuItem(parseInt(itemId));
      return {
        data: { success: true },
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: { success: false },
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },

  getOrders: async (courseId: string, params: Record<string, any> = {}): Promise<ApiResponse<any[]>> => {
    if (useMockData) {
      return {
        data: [],
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      const data = await xanoAPI.getOrders(courseId, { type: 'food', ...params });
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: [],
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },
};

export const analyticsAPI = {
  getOverview: async (courseId: string, timeView: 'daily' | 'weekly' | 'monthly' = 'daily'): Promise<ApiResponse<any>> => {
    if (useMockData) {
      // Return mock analytics data
      return {
        data: {
          revenue: 250000,
          bookings: 1250,
          utilization: 85,
          trends: {
            monthly: [12000, 15000, 18000, 22000, 25000, 28000, 30000, 32000, 35000, 38000, 40000, 42000]
          }
        },
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      const data = await xanoAPI.getAnalytics(courseId, {
        type: 'overview',
        timeframe: timeView,
      });
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: {
          revenue: 0,
          bookings: 0,
          utilization: 0,
          trends: { monthly: [] }
        },
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },

  getRevenueReport: async (courseId: string, params: Record<string, any> = {}): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return {
        data: {
          totalRevenue: 250000,
          breakdown: {
            proShop: 50000,
            teeTime: 150000,
            foodBeverage: 30000,
            events: 20000
          }
        },
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      const data = await xanoAPI.getAnalytics(courseId, {
        type: 'revenue',
        ...params,
      });
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: { totalRevenue: 0, breakdown: {} },
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },
};

export const backOfficeAPI = {
  getOverview: async (courseId: string, date: string, timeView: 'daily' | 'weekly' | 'monthly' = 'daily'): Promise<ApiResponse<any>> => {
    if (useMockData) {
      // Return mock back office data
      return {
        data: {
          shifts: [
            {
              id: '1',
              employeeName: 'John Smith',
              role: 'Manager',
              department: 'Operations',
              clockIn: '08:00 AM',
              clockOut: '05:00 PM',
              hours: 8,
              status: 'active',
              avatar: null
            }
          ],
          payroll: [
            {
              department: 'Operations',
              totalHours: 40,
              totalPay: 800
            }
          ]
        },
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      const staffData = await xanoAPI.getStaff(courseId);
      // Transform staff data to match BackOfficeData interface
      const transformedData = {
        shifts: staffData.map((staff: any) => ({
          id: staff.id,
          employeeName: staff.name || `${staff.first_name} ${staff.last_name}`,
          role: staff.role || staff.position,
          department: staff.department,
          clockIn: staff.clock_in_time,
          clockOut: staff.clock_out_time,
          hours: staff.hours_worked || 0,
          status: staff.status || 'active',
          avatar: staff.avatar_url
        })),
        payroll: [] // This would need additional API calls for payroll data
      };

      return {
        data: transformedData,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: { shifts: [], payroll: [] },
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },

  getStaff: async (courseId: string): Promise<ApiResponse<any[]>> => {
    if (useMockData) {
      return {
        data: [
          {
            id: '1',
            name: 'John Smith',
            role: 'Manager',
            department: 'Operations',
            status: 'active'
          }
        ],
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      const data = await xanoAPI.getStaff(courseId);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: [],
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },

  createStaff: async (courseId: string, data: any): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return {
        data: { id: Date.now().toString(), ...data },
        status: 201,
        statusText: 'Created'
      };
    }
    try {
      const result = await xanoAPI.createStaff({ ...data, course_id: parseInt(courseId) });
      return {
        data: result,
        status: 201,
        statusText: 'Created'
      };
    } catch (error: any) {
      return {
        data: null,
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },

  updateStaff: async (courseId: string, staffId: string, data: any): Promise<ApiResponse<any>> => {
    if (useMockData) {
      return {
        data: { id: staffId, ...data },
        status: 200,
        statusText: 'OK'
      };
    }
    try {
      const result = await xanoAPI.updateStaff(parseInt(staffId), data);
      return {
        data: result,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        data: null,
        status: error.response?.status || 500,
        statusText: error.message || 'Error'
      };
    }
  },
};

export const notificationsAPI = {
  getNotifications: (courseId: string, unreadOnly: boolean = false): Promise<ApiResponse<any[]>> => 
    api.get('/notifications', { params: { courseId, unreadOnly } }),
};

// Mock data for Restaurant POS
const mockMenuItems = [
  { id: 1, name: 'Chicken Wings', price: 12, category: 'Starters', color: '#ef4444', route: 'kitchen', modifiers: ['No sauce', 'Extra spicy', 'Ranch'] },
  { id: 2, name: 'Meat Sticks', price: 10, category: 'Starters', color: '#ef4444', route: 'kitchen', modifiers: ['BBQ', 'Teriyaki'] },
  { id: 3, name: 'Carpaccio', price: 14, category: 'Starters', color: '#ef4444', route: 'kitchen', modifiers: ['Extra lemon'] },
  { id: 4, name: 'Rump Steak', price: 24, category: 'Mains', color: '#ef4444', route: 'kitchen', modifiers: ['Rare', 'Medium', 'Well-done'] },
  { id: 5, name: 'Bruschetta', price: 9, category: 'Starters', color: '#22c55e', route: 'kitchen', modifiers: ['No garlic'] },
  { id: 6, name: 'Thai Salad', price: 10, category: 'Salads', color: '#22c55e', route: 'kitchen', modifiers: ['No peanuts'] },
  { id: 7, name: 'Spinach Salad', price: 11, category: 'Salads', color: '#22c55e', route: 'kitchen', modifiers: ['No onions'] },
  { id: 8, name: 'Fish Tacos', price: 14, category: 'Mains', color: '#0ea5e9', route: 'kitchen', modifiers: ['No slaw'] },
  { id: 9, name: 'Grilled Tuna', price: 16, category: 'Mains', color: '#0ea5e9', route: 'kitchen', modifiers: ['Rare', 'Medium', 'Well-done'] },
  { id: 10, name: 'Salmon', price: 16, category: 'Mains', color: '#0ea5e9', route: 'kitchen', modifiers: ['No skin'] },
  { id: 11, name: 'Coquilles', price: 18, category: 'Mains', color: '#0ea5e9', route: 'kitchen', modifiers: [] },
  { id: 12, name: 'Ice Cream', price: 6, category: 'Desserts', color: '#f59e42', route: 'kitchen', modifiers: ['Chocolate', 'Vanilla', 'Strawberry'] },
  { id: 13, name: 'Pancakes', price: 8, category: 'Desserts', color: '#f59e42', route: 'kitchen', modifiers: ['Maple syrup', 'No butter'] },
  { id: 14, name: 'Choco Mousse', price: 8, category: 'Desserts', color: '#f59e42', route: 'kitchen', modifiers: [] },
  { id: 15, name: 'Chocolate Cake', price: 8, category: 'Desserts', color: '#f59e42', route: 'kitchen', modifiers: [] },
  { id: 16, name: 'Lunch Menu', price: 20, category: 'Menus', color: '#a78bfa', route: 'kitchen', modifiers: [] },
  { id: 17, name: 'Dinner Menu', price: 22, category: 'Menus', color: '#a78bfa', route: 'kitchen', modifiers: [] },
  { id: 18, name: 'Friends/Family 10%', price: -10, category: 'Discounts', color: '#22c55e', route: 'kitchen', modifiers: [] },
  { id: 19, name: 'Cola', price: 3, color: '#3b82f6', category: 'Drinks', route: 'bar', modifiers: ['No ice', 'Lemon'] },
  { id: 20, name: 'Diet Cola', price: 3, color: '#3b82f6', category: 'Drinks', route: 'bar', modifiers: ['No ice', 'Lime'] },
  { id: 21, name: 'Iced Tea', price: 3, color: '#3b82f6', category: 'Drinks', route: 'bar', modifiers: ['No ice', 'Lemon'] },
  { id: 22, name: 'Lemonade', price: 3, color: '#3b82f6', category: 'Drinks', route: 'bar', modifiers: ['No ice'] },
];

const mockTables = [
  { id: 'BT-01', name: 'Bar Table 1' },
  { id: 'BT-02', name: 'Bar Table 2' },
  { id: 'T-01', name: 'Table 1' },
  { id: 'T-02', name: 'Table 2' },
  { id: 'T-03', name: 'Table 3' },
];

const mockUsers = [
  { id: 1, name: 'John Doe', email: '<EMAIL>' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>' },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>' },
];

const mockOrders = [
  { id: 1, table_id: 'T-01', status: 'pending', total: 45.50, created_at: '2024-01-15T10:30:00Z' },
  { id: 2, table_id: 'BT-01', status: 'completed', total: 32.75, created_at: '2024-01-15T09:15:00Z' },
];

const mockTransactions = [
  { id: 1, golf_course_id: 1, transaction_type: 'food_and_beverage', status: 'pending', total: 45.50, created_at: '2024-01-15T10:30:00Z' },
  { id: 2, golf_course_id: 1, transaction_type: 'food_and_beverage', status: 'completed', total: 32.75, created_at: '2024-01-15T09:15:00Z' },
];

const mockItemTransactions = [
  { id: 1, quantity: 2, food_and_beverage_item_id: 1, transaction_id: 1, order_status: 'Pending', claimed_by_employee_id: 1, location: 'Kitchen' },
  { id: 2, quantity: 1, food_and_beverage_item_id: 19, transaction_id: 1, order_status: 'Ready', claimed_by_employee_id: 2, location: 'Bar' },
];

// Mock API services for Restaurant POS
export const menuItems = {
  getAll: async () => Promise.resolve(mockMenuItems),
  getById: async (id: number) => Promise.resolve(mockMenuItems.find(item => item.id === id)),
  create: async (data: any) => Promise.resolve({ id: Date.now(), ...data }),
  update: async (id: number, data: any) => Promise.resolve({ id, ...data }),
  delete: async (id: number) => Promise.resolve(true),
};

export const tables = {
  getAll: async () => Promise.resolve(mockTables),
  getById: async (id: string) => Promise.resolve(mockTables.find(table => table.id === id)),
  create: async (data: any) => Promise.resolve({ id: `T-${Date.now()}`, ...data }),
  update: async (id: string, data: any) => Promise.resolve({ id, ...data }),
  delete: async (id: string) => Promise.resolve(true),
};

export const orders = {
  getAll: async () => Promise.resolve(mockOrders),
  getById: async (id: number) => Promise.resolve(mockOrders.find(order => order.id === id)),
  create: async (data: any) => Promise.resolve({ id: Date.now(), ...data }),
  update: async (id: number, data: any) => Promise.resolve({ id, ...data }),
  delete: async (id: number) => Promise.resolve(true),
};

export const transactions = {
  getAll: async () => Promise.resolve(mockTransactions),
  getById: async (id: number) => Promise.resolve(mockTransactions.find(transaction => transaction.id === id)),
  create: async (data: any) => Promise.resolve({ id: Date.now(), ...data }),
  update: async (id: number, data: any) => Promise.resolve({ id, ...data }),
  delete: async (id: number) => Promise.resolve(true),
};

export const itemTransactions = {
  getAll: async () => Promise.resolve(mockItemTransactions),
  getById: async (id: number) => Promise.resolve(mockItemTransactions.find(item => item.id === id)),
  create: async (data: any) => Promise.resolve({ id: Date.now(), ...data }),
  update: async (id: number, data: any) => Promise.resolve({ id, ...data }),
  delete: async (id: number) => Promise.resolve(true),
};

export const users = {
  getAll: async () => Promise.resolve(mockUsers),
  getById: async (id: number) => Promise.resolve(mockUsers.find(user => user.id === id)),
  create: async (data: any) => Promise.resolve({ id: Date.now(), ...data }),
  update: async (id: number, data: any) => Promise.resolve({ id, ...data }),
  delete: async (id: number) => Promise.resolve(true),
};

export const transformMenuItem = (item: any) => ({
  id: item.id.toString(),
  name: item.name,
  price: item.price,
  category: item.category,
  color: item.color,
  route: item.route,
  modifiers: item.modifiers || [],
}); 