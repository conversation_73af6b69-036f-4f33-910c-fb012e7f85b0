import xanoAPI from '../api/XanoAPIClient';
import config from '../config';

// API Response interface
export interface ApiResponse<T> {
  data: T;
  status: number;
  statusText: string;
}

// Dashboard API
export const dashboardAPI = {
  getOverview: async (courseId: string, timeframe: 'daily' | 'weekly' | 'monthly' = 'monthly'): Promise<ApiResponse<any>> => {
    try {
      const data = await xanoAPI.getDashboardMetrics(courseId, timeframe);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Dashboard API Error:', error.message);
      throw new Error(`Failed to load dashboard data: ${error.message}`);
    }
  },
};

// Roster API
export const rosterAPI = {
  getGolfers: async (courseId: string, params: Record<string, any> = {}): Promise<ApiResponse<any[]>> => {
    try {
      const data = await xanoAPI.getGolfers(courseId, params);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Roster API Error:', error.message);
      throw new Error(`Failed to load golfers: ${error.message}`);
    }
  },

  createGolfer: async (courseId: string, data: any): Promise<ApiResponse<any>> => {
    try {
      const result = await xanoAPI.createGolfer({ ...data, course_id: parseInt(courseId) });
      return {
        data: result,
        status: 201,
        statusText: 'Created'
      };
    } catch (error: any) {
      console.error('Create Golfer API Error:', error.message);
      throw new Error(`Failed to create golfer: ${error.message}`);
    }
  },

  updateGolfer: async (courseId: string, golferId: string, data: any): Promise<ApiResponse<any>> => {
    try {
      const result = await xanoAPI.updateGolfer(parseInt(golferId), data);
      return {
        data: result,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Update Golfer API Error:', error.message);
      throw new Error(`Failed to update golfer: ${error.message}`);
    }
  },

  deleteGolfer: async (golferId: string): Promise<ApiResponse<any>> => {
    try {
      await xanoAPI.deleteGolfer(parseInt(golferId));
      return {
        data: { success: true },
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Delete Golfer API Error:', error.message);
      throw new Error(`Failed to delete golfer: ${error.message}`);
    }
  },

  getMetrics: async (courseId: string): Promise<ApiResponse<any>> => {
    try {
      const data = await xanoAPI.getDashboardMetrics(courseId);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Metrics API Error:', error.message);
      throw new Error(`Failed to load metrics: ${error.message}`);
    }
  },
};

// Tee Times API
export const teeTimesAPI = {
  getTeeTimesList: async (courseId: string, view: 'daily' | 'weekly' | 'monthly' = 'daily', date: string): Promise<ApiResponse<any[]>> => {
    try {
      const data = await xanoAPI.getTeeTimes(courseId, date, view);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Tee Times API Error:', error.message);
      throw new Error(`Failed to load tee times: ${error.message}`);
    }
  },

  createTeeTime: async (courseId: string, data: any): Promise<ApiResponse<any>> => {
    try {
      const result = await xanoAPI.createTeeTime({ ...data, course_id: parseInt(courseId) });
      return {
        data: result,
        status: 201,
        statusText: 'Created'
      };
    } catch (error: any) {
      console.error('Create Tee Time API Error:', error.message);
      throw new Error(`Failed to create tee time: ${error.message}`);
    }
  },

  updateTeeTime: async (courseId: string, teeTimeId: string, data: any): Promise<ApiResponse<any>> => {
    try {
      const result = await xanoAPI.updateTeeTime(parseInt(teeTimeId), data);
      return {
        data: result,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Update Tee Time API Error:', error.message);
      throw new Error(`Failed to update tee time: ${error.message}`);
    }
  },

  deleteTeeTime: async (courseId: string, teeTimeId: string): Promise<ApiResponse<any>> => {
    try {
      await xanoAPI.deleteTeeTime(parseInt(teeTimeId));
      return {
        data: { success: true },
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Delete Tee Time API Error:', error.message);
      throw new Error(`Failed to delete tee time: ${error.message}`);
    }
  },
};

// Pro Shop API
export const proShopAPI = {
  getInventory: async (courseId: string, params: Record<string, any> = {}): Promise<ApiResponse<any[]>> => {
    try {
      const data = await xanoAPI.getProducts(courseId, params);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Pro Shop API Error:', error.message);
      throw new Error(`Failed to load inventory: ${error.message}`);
    }
  },

  createItem: async (courseId: string, data: any): Promise<ApiResponse<any>> => {
    try {
      const result = await xanoAPI.createProduct({ ...data, course_id: parseInt(courseId) });
      return {
        data: result,
        status: 201,
        statusText: 'Created'
      };
    } catch (error: any) {
      console.error('Create Product API Error:', error.message);
      throw new Error(`Failed to create product: ${error.message}`);
    }
  },

  updateItem: async (courseId: string, itemId: string, data: any): Promise<ApiResponse<any>> => {
    try {
      const result = await xanoAPI.updateProduct(parseInt(itemId), data);
      return {
        data: result,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Update Product API Error:', error.message);
      throw new Error(`Failed to update product: ${error.message}`);
    }
  },

  deleteItem: async (courseId: string, itemId: string): Promise<ApiResponse<any>> => {
    try {
      await xanoAPI.deleteProduct(parseInt(itemId));
      return {
        data: { success: true },
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Delete Product API Error:', error.message);
      throw new Error(`Failed to delete product: ${error.message}`);
    }
  },
};

// 19th Hole API
export const nineteenthHoleAPI = {
  getMenu: async (courseId: string, params: Record<string, any> = {}): Promise<ApiResponse<any[]>> => {
    try {
      const data = await xanoAPI.getMenuItems(courseId, params);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('19th Hole API Error:', error.message);
      throw new Error(`Failed to load menu: ${error.message}`);
    }
  },

  createMenuItem: async (courseId: string, data: any): Promise<ApiResponse<any>> => {
    try {
      const result = await xanoAPI.createMenuItem({ ...data, course_id: parseInt(courseId) });
      return {
        data: result,
        status: 201,
        statusText: 'Created'
      };
    } catch (error: any) {
      console.error('Create Menu Item API Error:', error.message);
      throw new Error(`Failed to create menu item: ${error.message}`);
    }
  },

  updateMenuItem: async (courseId: string, itemId: string, data: any): Promise<ApiResponse<any>> => {
    try {
      const result = await xanoAPI.updateMenuItem(parseInt(itemId), data);
      return {
        data: result,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Update Menu Item API Error:', error.message);
      throw new Error(`Failed to update menu item: ${error.message}`);
    }
  },

  deleteMenuItem: async (courseId: string, itemId: string): Promise<ApiResponse<any>> => {
    try {
      await xanoAPI.deleteMenuItem(parseInt(itemId));
      return {
        data: { success: true },
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Delete Menu Item API Error:', error.message);
      throw new Error(`Failed to delete menu item: ${error.message}`);
    }
  },

  getOrders: async (courseId: string, params: Record<string, any> = {}): Promise<ApiResponse<any[]>> => {
    try {
      const data = await xanoAPI.getOrders(courseId, { type: 'food', ...params });
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Orders API Error:', error.message);
      throw new Error(`Failed to load orders: ${error.message}`);
    }
  },
};

// Analytics API
export const analyticsAPI = {
  getOverview: async (courseId: string, timeView: 'daily' | 'weekly' | 'monthly' = 'daily'): Promise<ApiResponse<any>> => {
    try {
      const data = await xanoAPI.getAnalytics(courseId, {
        type: 'overview',
        timeframe: timeView,
      });
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Analytics API Error:', error.message);
      throw new Error(`Failed to load analytics: ${error.message}`);
    }
  },

  getRevenueReport: async (courseId: string, params: Record<string, any> = {}): Promise<ApiResponse<any>> => {
    try {
      const data = await xanoAPI.getAnalytics(courseId, {
        type: 'revenue',
        ...params,
      });
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Revenue Report API Error:', error.message);
      throw new Error(`Failed to load revenue report: ${error.message}`);
    }
  },
};

// Back Office API
export const backOfficeAPI = {
  getOverview: async (courseId: string, date: string, timeView: 'daily' | 'weekly' | 'monthly' = 'daily'): Promise<ApiResponse<any>> => {
    try {
      const staffData = await xanoAPI.getStaff(courseId);
      const transformedData = {
        shifts: staffData.map((staff: any) => ({
          id: staff.id,
          employeeName: staff.name || `${staff.first_name} ${staff.last_name}`,
          role: staff.role || staff.position,
          department: staff.department,
          clockIn: staff.clock_in_time,
          clockOut: staff.clock_out_time,
          hours: staff.hours_worked || 0,
          status: staff.status || 'active',
          avatar: staff.avatar_url
        })),
        payroll: []
      };
      
      return {
        data: transformedData,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Back Office API Error:', error.message);
      throw new Error(`Failed to load back office data: ${error.message}`);
    }
  },

  getStaff: async (courseId: string): Promise<ApiResponse<any[]>> => {
    try {
      const data = await xanoAPI.getStaff(courseId);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Staff API Error:', error.message);
      throw new Error(`Failed to load staff: ${error.message}`);
    }
  },

  createStaff: async (courseId: string, data: any): Promise<ApiResponse<any>> => {
    try {
      const result = await xanoAPI.createStaff({ ...data, course_id: parseInt(courseId) });
      return {
        data: result,
        status: 201,
        statusText: 'Created'
      };
    } catch (error: any) {
      console.error('Create Staff API Error:', error.message);
      throw new Error(`Failed to create staff: ${error.message}`);
    }
  },

  updateStaff: async (courseId: string, staffId: string, data: any): Promise<ApiResponse<any>> => {
    try {
      const result = await xanoAPI.updateStaff(parseInt(staffId), data);
      return {
        data: result,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Update Staff API Error:', error.message);
      throw new Error(`Failed to update staff: ${error.message}`);
    }
  },
};

// Weather and Course APIs
export const weatherAPI = {
  getWeather: async (courseId: string, date: string): Promise<ApiResponse<any>> => {
    try {
      const data = await xanoAPI.getWeather(courseId, date);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Weather API Error:', error.message);
      throw new Error(`Failed to load weather: ${error.message}`);
    }
  },
};

export const courseAPI = {
  getCourseInfo: async (courseId: string): Promise<ApiResponse<any>> => {
    try {
      const data = await xanoAPI.getCourse(courseId);
      return {
        data,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      console.error('Course API Error:', error.message);
      throw new Error(`Failed to load course info: ${error.message}`);
    }
  },
};
