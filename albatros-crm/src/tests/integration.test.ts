/**
 * Integration Tests for XANO Backend Integration
 * 
 * This test suite validates that all modules properly integrate with the XANO backend
 * and handle both success and error scenarios correctly.
 */

import { 
  dashboardAPI, 
  rosterAPI, 
  teeTimesAPI, 
  proShopAPI, 
  nineteenthHoleAPI, 
  analyticsAPI, 
  backOfficeAPI 
} from '../services/api';
import config from '../config';

// Mock course ID for testing
const TEST_COURSE_ID = config.courseId;

describe('XANO Backend Integration Tests', () => {
  
  describe('Dashboard API Integration', () => {
    test('should fetch dashboard overview data', async () => {
      const response = await dashboardAPI.getOverview(TEST_COURSE_ID, 'monthly');
      
      expect(response).toBeDefined();
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      
      if (!config.useMockData) {
        // When using real XANO data, verify structure
        expect(typeof response.data.total_revenue).toBe('number');
        expect(typeof response.data.total_bookings).toBe('number');
        expect(typeof response.data.total_golfers).toBe('number');
      }
    });
  });

  describe('Roster API Integration', () => {
    test('should fetch golfers list', async () => {
      const response = await rosterAPI.getGolfers(TEST_COURSE_ID);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
    });

    test('should create a new golfer', async () => {
      const newGolfer = {
        first_name: 'Test',
        last_name: 'Golfer',
        phone: '555-0123',
        email: '<EMAIL>',
        stars: 4,
        is_member: true
      };

      const response = await rosterAPI.createGolfer(TEST_COURSE_ID, newGolfer);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(201);
      expect(response.data).toBeDefined();
    });

    test('should get roster metrics', async () => {
      const response = await rosterAPI.getMetrics(TEST_COURSE_ID);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
    });
  });

  describe('Tee Times API Integration', () => {
    test('should fetch tee times list', async () => {
      const today = new Date().toISOString().split('T')[0];
      const response = await teeTimesAPI.getTeeTimesList(TEST_COURSE_ID, 'daily', today);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
    });

    test('should create a new tee time', async () => {
      const newTeeTime = {
        date: new Date().toISOString().split('T')[0],
        time: '10:00',
        player_name: 'Test Player',
        number_of_players: 2,
        status: 'available'
      };

      const response = await teeTimesAPI.createTeeTime(TEST_COURSE_ID, newTeeTime);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(201);
      expect(response.data).toBeDefined();
    });
  });

  describe('Pro Shop API Integration', () => {
    test('should fetch inventory items', async () => {
      const response = await proShopAPI.getInventory(TEST_COURSE_ID);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
    });

    test('should create a new inventory item', async () => {
      const newItem = {
        name: 'Test Golf Ball',
        category: 'Equipment',
        price: 29.99,
        quantity: 100,
        description: 'Test golf ball for integration testing'
      };

      const response = await proShopAPI.createItem(TEST_COURSE_ID, newItem);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(201);
      expect(response.data).toBeDefined();
    });
  });

  describe('19th Hole API Integration', () => {
    test('should fetch menu items', async () => {
      const response = await nineteenthHoleAPI.getMenu(TEST_COURSE_ID);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
    });

    test('should create a new menu item', async () => {
      const newMenuItem = {
        name: 'Test Sandwich',
        description: 'A test sandwich for integration testing',
        price: 12.99,
        category: 'sandwiches',
        is_available: true
      };

      const response = await nineteenthHoleAPI.createMenuItem(TEST_COURSE_ID, newMenuItem);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(201);
      expect(response.data).toBeDefined();
    });

    test('should fetch orders', async () => {
      const response = await nineteenthHoleAPI.getOrders(TEST_COURSE_ID);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
    });
  });

  describe('Analytics API Integration', () => {
    test('should fetch analytics overview', async () => {
      const response = await analyticsAPI.getOverview(TEST_COURSE_ID, 'monthly');
      
      expect(response).toBeDefined();
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      
      if (!config.useMockData) {
        expect(typeof response.data.revenue).toBe('number');
        expect(typeof response.data.bookings).toBe('number');
        expect(typeof response.data.utilization).toBe('number');
      }
    });

    test('should fetch revenue report', async () => {
      const response = await analyticsAPI.getRevenueReport(TEST_COURSE_ID);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
    });
  });

  describe('Back Office API Integration', () => {
    test('should fetch back office overview', async () => {
      const today = new Date().toISOString();
      const response = await backOfficeAPI.getOverview(TEST_COURSE_ID, today, 'daily');
      
      expect(response).toBeDefined();
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      expect(Array.isArray(response.data.shifts)).toBe(true);
    });

    test('should fetch staff list', async () => {
      const response = await backOfficeAPI.getStaff(TEST_COURSE_ID);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
    });

    test('should create new staff member', async () => {
      const newStaff = {
        name: 'Test Employee',
        role: 'Staff',
        department: 'Operations',
        status: 'active'
      };

      const response = await backOfficeAPI.createStaff(TEST_COURSE_ID, newStaff);
      
      expect(response).toBeDefined();
      expect(response.status).toBe(201);
      expect(response.data).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid course ID gracefully', async () => {
      const invalidCourseId = 'invalid-course-id';
      
      const response = await rosterAPI.getGolfers(invalidCourseId);
      
      // Should not throw an error, but return error response
      expect(response).toBeDefined();
      expect(response.status).toBeGreaterThanOrEqual(400);
    });

    test('should handle network errors gracefully', async () => {
      // This test would require mocking network failures
      // For now, we just ensure the API methods exist and are callable
      expect(typeof dashboardAPI.getOverview).toBe('function');
      expect(typeof rosterAPI.getGolfers).toBe('function');
      expect(typeof teeTimesAPI.getTeeTimesList).toBe('function');
      expect(typeof proShopAPI.getInventory).toBe('function');
      expect(typeof nineteenthHoleAPI.getMenu).toBe('function');
      expect(typeof analyticsAPI.getOverview).toBe('function');
      expect(typeof backOfficeAPI.getOverview).toBe('function');
    });
  });

  describe('Configuration Validation', () => {
    test('should have proper configuration setup', () => {
      expect(config.courseId).toBeDefined();
      expect(config.xanoBaseUrl).toBeDefined();
      expect(typeof config.useMockData).toBe('boolean');
    });

    test('should switch between mock and real data based on configuration', () => {
      // This test validates that the configuration properly controls data source
      expect(config.useMockData).toBeDefined();
      
      if (config.useMockData) {
        console.log('Running tests with mock data');
      } else {
        console.log('Running tests with XANO backend');
        expect(config.xanoBaseUrl).toContain('xano.io');
      }
    });
  });
});
